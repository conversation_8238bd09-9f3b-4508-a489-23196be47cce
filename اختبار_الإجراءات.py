#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع إجراءات لوحة الإدارة
Test all admin panel actions
"""

import requests
import json
from database import DatabaseManager
from camera_manager import CameraManager

def test_camera_crud():
    """اختبار عمليات CRUD للكاميرات"""
    print("📹 اختبار عمليات إدارة الكاميرات...")
    print("📹 Testing Camera CRUD Operations...")
    
    try:
        db = DatabaseManager()
        
        # اختبار إضافة كاميرا
        test_camera_id = db.add_camera(
            name="كاميرا اختبار الإجراءات",
            camera_type="dahua",
            ip_address="*************",
            port=554,
            username="admin",
            password="test123",
            channel=2,
            subtype=1,
            location_name="اختبار الإجراءات"
        )
        
        if test_camera_id:
            print(f"   ✅ إضافة كاميرا: نجح (ID: {test_camera_id})")
            print(f"   ✅ Add camera: Success (ID: {test_camera_id})")
            
            # اختبار تحديث الكاميرا
            update_success = db.update_camera(
                camera_id=test_camera_id,
                name="كاميرا محدثة",
                location_name="موقع محدث"
            )
            
            if update_success:
                print("   ✅ تحديث كاميرا: نجح")
                print("   ✅ Update camera: Success")
            
            # اختبار تفعيل/إلغاء تفعيل
            toggle_success = db.toggle_camera_active(test_camera_id, False)
            if toggle_success:
                print("   ✅ إلغاء تفعيل كاميرا: نجح")
                print("   ✅ Deactivate camera: Success")
            
            toggle_success = db.toggle_camera_active(test_camera_id, True)
            if toggle_success:
                print("   ✅ تفعيل كاميرا: نجح")
                print("   ✅ Activate camera: Success")
            
            # اختبار حذف الكاميرا
            delete_success = db.delete_camera(test_camera_id)
            if delete_success:
                print("   ✅ حذف كاميرا: نجح")
                print("   ✅ Delete camera: Success")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار CRUD: {str(e)}")
        print(f"   ❌ CRUD test error: {str(e)}")
        return False

def test_camera_actions():
    """اختبار إجراءات الكاميرا"""
    print("\n🎬 اختبار إجراءات الكاميرا...")
    print("🎬 Testing Camera Actions...")
    
    try:
        camera_manager = CameraManager()
        
        # اختبار بدء التسجيل
        recording_result = camera_manager.start_recording(1)
        if recording_result:
            print("   ✅ بدء التسجيل: نجح")
            print("   ✅ Start recording: Success")
        else:
            print("   ⚠️ بدء التسجيل: فشل (متوقع للكاميرات التجريبية)")
            print("   ⚠️ Start recording: Failed (expected for demo cameras)")
        
        # اختبار التقاط صورة
        snapshot_result = camera_manager.take_snapshot(1)
        if snapshot_result:
            print(f"   ✅ التقاط صورة: نجح ({snapshot_result})")
            print(f"   ✅ Take snapshot: Success ({snapshot_result})")
        else:
            print("   ⚠️ التقاط صورة: فشل")
            print("   ⚠️ Take snapshot: Failed")
        
        # اختبار إعادة تشغيل الكاميرا
        restart_result = camera_manager.restart_camera(1)
        if restart_result:
            print("   ✅ إعادة تشغيل الكاميرا: نجح")
            print("   ✅ Restart camera: Success")
        else:
            print("   ⚠️ إعادة تشغيل الكاميرا: فشل (متوقع للكاميرات التجريبية)")
            print("   ⚠️ Restart camera: Failed (expected for demo cameras)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الإجراءات: {str(e)}")
        print(f"   ❌ Actions test error: {str(e)}")
        return False

def test_admin_api():
    """اختبار API لوحة الإدارة"""
    print("\n🔌 اختبار API لوحة الإدارة...")
    print("🔌 Testing Admin Panel API...")
    
    try:
        # اختبار الحصول على الكاميرات
        response = requests.get('http://localhost:4040/api/cameras', timeout=5)
        
        if response.status_code == 200:
            cameras = response.json()
            print(f"   ✅ الحصول على الكاميرات: نجح ({len(cameras)} كاميرا)")
            print(f"   ✅ Get cameras: Success ({len(cameras)} cameras)")
        elif response.status_code in [401, 302]:
            print("   ✅ حماية API: تعمل (مطلوب تسجيل دخول)")
            print("   ✅ API protection: Working (login required)")
        else:
            print(f"   ⚠️ استجابة غير متوقعة: {response.status_code}")
            print(f"   ⚠️ Unexpected response: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {str(e)}")
        print(f"   ❌ API test error: {str(e)}")
        return False

def test_admin_page():
    """اختبار صفحة لوحة الإدارة"""
    print("\n📄 اختبار صفحة لوحة الإدارة...")
    print("📄 Testing Admin Panel Page...")
    
    try:
        # اختبار صفحة الإدارة
        response = requests.get('http://localhost:4040/admin', timeout=5)
        
        if response.status_code == 200:
            print("   ✅ صفحة الإدارة: تعمل")
            print("   ✅ Admin page: Working")
            
            # التحقق من وجود عناصر مهمة
            content = response.text
            if 'btn-group' in content and 'dropdown-toggle' in content:
                print("   ✅ أزرار الإجراءات: موجودة")
                print("   ✅ Action buttons: Present")
            
        elif response.status_code == 302:
            print("   ✅ صفحة الإدارة: محمية (إعادة توجيه لتسجيل الدخول)")
            print("   ✅ Admin page: Protected (redirect to login)")
        else:
            print(f"   ❌ خطأ في صفحة الإدارة: {response.status_code}")
            return False
        
        # اختبار الصفحة العربية
        ar_response = requests.get('http://localhost:4040/admin?language=ar', timeout=5)
        
        if ar_response.status_code in [200, 302]:
            print("   ✅ الدعم العربي: يعمل")
            print("   ✅ Arabic support: Working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الصفحة: {str(e)}")
        print(f"   ❌ Page test error: {str(e)}")
        return False

def show_admin_features():
    """عرض مميزات لوحة الإدارة"""
    print("\n" + "=" * 60)
    print("⚙️ مميزات لوحة الإدارة المفعلة")
    print("⚙️ Activated Admin Panel Features")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        cameras = db.get_cameras(active_only=False)
        
        print(f"📊 إحصائيات الكاميرات:")
        print(f"   إجمالي الكاميرات: {len(cameras)}")
        
        active_cameras = [c for c in cameras if c.get('is_active', True)]
        print(f"   الكاميرات النشطة: {len(active_cameras)}")
        
        print(f"\n🎛️ الإجراءات المتاحة:")
        actions = [
            "عرض الكاميرا - View Camera",
            "اختبار الاتصال - Test Connection", 
            "تعديل الكاميرا - Edit Camera",
            "تشغيل/إيقاف الكاميرا - Start/Stop Camera",
            "إعادة تشغيل الكاميرا - Restart Camera",
            "بدء التسجيل - Start Recording",
            "التقاط صورة - Take Snapshot",
            "تفعيل/إلغاء تفعيل - Activate/Deactivate",
            "حذف الكاميرا - Delete Camera"
        ]
        
        for i, action in enumerate(actions, 1):
            print(f"   {i}. ✅ {action}")
        
        print(f"\n🌐 الوصول للوحة الإدارة:")
        print(f"   عربي: http://localhost:4040/admin?language=ar")
        print(f"   English: http://localhost:4040/admin?language=en")
        print(f"   🔑 تسجيل دخول: admin / admin123")
        
        print(f"\n📁 المجلدات المنشأة:")
        import os
        folders = ['recordings', 'snapshots']
        for folder in folders:
            if os.path.exists(folder):
                print(f"   ✅ {folder}/")
            else:
                print(f"   📁 {folder}/ (سيتم إنشاؤه عند الحاجة)")
        
    except Exception as e:
        print(f"❌ خطأ في عرض المميزات: {str(e)}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لإجراءات لوحة الإدارة")
    print("🧪 Comprehensive Admin Panel Actions Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار عمليات CRUD
    if test_camera_crud():
        tests_passed += 1
    
    # اختبار إجراءات الكاميرا
    if test_camera_actions():
        tests_passed += 1
    
    # اختبار API
    if test_admin_api():
        tests_passed += 1
    
    # اختبار الصفحة
    if test_admin_page():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار إجراءات لوحة الإدارة")
    print("📊 Admin Panel Actions Test Results")
    print("=" * 60)
    print(f"✅ نجح: {tests_passed}/{total_tests} اختبار")
    print(f"✅ Passed: {tests_passed}/{total_tests} tests")
    
    if tests_passed == total_tests:
        print("🎉 جميع اختبارات إجراءات لوحة الإدارة نجحت!")
        print("🎉 All admin panel actions tests passed!")
        print("✅ جميع الإجراءات مفعلة وتعمل بشكل مثالي")
        print("✅ All actions activated and working perfectly")
    else:
        print("⚠️ بعض اختبارات إجراءات لوحة الإدارة فشلت")
        print("⚠️ Some admin panel actions tests failed")
    
    # عرض مميزات لوحة الإدارة
    show_admin_features()

if __name__ == "__main__":
    main()
