#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للدعم العربي
Quick test for Arabic support
"""

import time
import webbrowser
from translations import get_text

def test_translations():
    """اختبار الترجمات"""
    print("🧪 اختبار نظام الترجمة...")
    print("🧪 Testing translation system...")
    
    # اختبار النصوص العربية
    arabic_texts = [
        'camera_monitor',
        'dashboard', 
        'admin',
        'live_cameras',
        'add_camera',
        'camera_name',
        'username',
        'password'
    ]
    
    print("\n📝 النصوص العربية:")
    print("📝 Arabic texts:")
    for key in arabic_texts:
        ar_text = get_text(key, 'ar')
        en_text = get_text(key, 'en')
        print(f"   {key}: {ar_text} | {en_text}")
    
    print("\n✅ نظام الترجمة يعمل بشكل صحيح!")
    print("✅ Translation system working correctly!")

def test_server():
    """اختبار الخادم"""
    print("\n🌐 اختبار الخادم...")
    print("🌐 Testing server...")
    
    try:
        import requests
        
        # اختبار الصفحة الرئيسية
        print("   📡 اختبار الصفحة الرئيسية...")
        response = requests.get('http://localhost:4040', timeout=5)
        
        if response.status_code == 200:
            print("   ✅ الخادم يعمل!")
            print("   ✅ Server is running!")
            
            # اختبار العربية
            print("   📡 اختبار الدعم العربي...")
            ar_response = requests.get('http://localhost:4040?language=ar', timeout=5)
            
            if 'مراقب الكاميرات' in ar_response.text:
                print("   ✅ الدعم العربي يعمل!")
                print("   ✅ Arabic support working!")
                return True
            else:
                print("   ⚠️ مشكلة في الدعم العربي")
                print("   ⚠️ Issue with Arabic support")
                return False
        else:
            print(f"   ❌ خطأ في الخادم: {response.status_code}")
            print(f"   ❌ Server error: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالخادم")
        print("   ❌ Cannot connect to server")
        return False
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        print(f"   ❌ Error: {str(e)}")
        return False

def open_browser_test():
    """فتح المتصفح للاختبار"""
    print("\n🌐 فتح المتصفح للاختبار...")
    print("🌐 Opening browser for testing...")
    
    try:
        # فتح بالعربية
        webbrowser.open('http://localhost:4040?language=ar')
        print("   ✅ تم فتح المتصفح بالعربية")
        print("   ✅ Browser opened with Arabic")
        
        time.sleep(2)
        
        # فتح بالإنجليزية
        webbrowser.open('http://localhost:4040?language=en')
        print("   ✅ تم فتح المتصفح بالإنجليزية")
        print("   ✅ Browser opened with English")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في فتح المتصفح: {str(e)}")
        print(f"   ❌ Error opening browser: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🎥 اختبار نظام مراقبة الكاميرات متعدد اللغات")
    print("🎥 Testing Multi-Language Camera Monitoring System")
    print("=" * 60)
    
    # اختبار الترجمات
    test_translations()
    
    # انتظار قليل
    print("\n⏳ انتظار بدء الخادم...")
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    # اختبار الخادم
    server_ok = test_server()
    
    if server_ok:
        # فتح المتصفح
        open_browser_test()
        
        print("\n" + "=" * 60)
        print("🎉 جميع الاختبارات نجحت!")
        print("🎉 All tests passed!")
        print("🌐 يمكنك الآن استخدام النظام:")
        print("🌐 You can now use the system:")
        print("   📱 عربي: http://localhost:4040?language=ar")
        print("   📱 English: http://localhost:4040?language=en")
        print("   🔑 admin / admin123")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في الاختبار!")
        print("❌ Test failed!")
        print("💡 تأكد من تشغيل الخادم أولاً:")
        print("💡 Make sure to start the server first:")
        print("   python app.py")
        print("=" * 60)

if __name__ == "__main__":
    main()
