#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تشغيل النظام المضبوط والصحيح
Script to run the corrected and complete system
"""

import os
import sys
import time
import subprocess
import webbrowser
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🎉 نظام مراقبة الكاميرات المضبوط والصحيح 🎉        ║
║                                                              ║
║     🚀 Camera Monitoring System - Complete & Correct 🚀     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    print("🔍 Checking requirements...")
    
    required_modules = ['flask', 'sqlite3', 'hashlib']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"   ❌ {module}")
    
    if missing_modules:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("💡 قم بتثبيتها باستخدام: pip install flask")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    print("✅ All requirements satisfied")
    return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    print("📁 Checking files...")
    
    required_files = ['app_complete.py']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            missing_files.append(file)
            print(f"   ❌ {file}")
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات موجودة")
    print("✅ All files present")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📂 إنشاء المجلدات...")
    print("📂 Creating directories...")
    
    directories = ['recordings', 'snapshots', 'static', 'templates']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   ✅ تم إنشاء: {directory}")
        else:
            print(f"   ✅ موجود: {directory}")
    
    print("✅ جميع المجلدات جاهزة")
    print("✅ All directories ready")

def test_system():
    """اختبار النظام"""
    print("\n🧪 اختبار النظام...")
    print("🧪 Testing system...")
    
    try:
        # اختبار استيراد النظام
        sys.path.insert(0, '.')
        import app_complete
        print("   ✅ تم تحميل النظام بنجاح")
        print("   ✅ System loaded successfully")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في تحميل النظام: {str(e)}")
        print(f"   ❌ System loading error: {str(e)}")
        return False

def start_system():
    """تشغيل النظام"""
    print("\n🚀 تشغيل النظام...")
    print("🚀 Starting system...")
    
    try:
        # تشغيل النظام
        process = subprocess.Popen([
            sys.executable, 'app_complete.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("   ✅ تم تشغيل النظام")
        print("   ✅ System started")
        
        # انتظار قصير للتأكد من التشغيل
        time.sleep(3)
        
        # فحص حالة العملية
        if process.poll() is None:
            print("   ✅ النظام يعمل بشكل طبيعي")
            print("   ✅ System running normally")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"   ❌ فشل في تشغيل النظام: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل النظام: {str(e)}")
        return None

def open_browser():
    """فتح المتصفح"""
    print("\n🌐 فتح المتصفح...")
    print("🌐 Opening browser...")
    
    url = "http://127.0.0.1:4040"
    
    try:
        webbrowser.open(url)
        print(f"   ✅ تم فتح: {url}")
        print(f"   ✅ Opened: {url}")
        return True
    except Exception as e:
        print(f"   ⚠️ لم يتم فتح المتصفح تلقائياً: {str(e)}")
        print(f"   ⚠️ Browser not opened automatically: {str(e)}")
        print(f"   💡 افتح المتصفح يدوياً: {url}")
        print(f"   💡 Open browser manually: {url}")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("\n" + "="*60)
    print("📋 معلومات النظام")
    print("📋 System Information")
    print("="*60)
    
    info = [
        ("🌐 الرابط الرئيسي", "http://127.0.0.1:4040"),
        ("🔑 تسجيل الدخول", "admin / admin123"),
        ("📹 إدارة الكاميرات", "http://127.0.0.1:4040/cameras"),
        ("👥 إدارة المستخدمين", "http://127.0.0.1:4040/users"),
        ("🚨 أحداث الحركة", "http://127.0.0.1:4040/events"),
        ("📊 API الإحصائيات", "http://127.0.0.1:4040/api/stats"),
        ("🏥 حالة النظام", "http://127.0.0.1:4040/api/system/health")
    ]
    
    for label, value in info:
        print(f"{label}: {value}")
    
    print("\n" + "="*60)
    print("🎯 المميزات المتاحة:")
    print("🎯 Available Features:")
    print("="*60)
    
    features = [
        "✅ جميع الصفحات تعمل بشكل صحيح",
        "✅ قاعدة بيانات محسنة مع بيانات تجريبية",
        "✅ واجهات جميلة ومتجاوبة",
        "✅ API متكامل للبيانات",
        "✅ أمان عالي وحماية الجلسات",
        "✅ سرعة عالية وأداء محسن",
        "✅ إحصائيات حية ومؤشرات الأداء",
        "✅ سجل النظام ومراقبة الأحداث"
    ]
    
    for feature in features:
        print(f"   {feature}")

def show_usage_tips():
    """عرض نصائح الاستخدام"""
    print("\n" + "="*60)
    print("💡 نصائح الاستخدام")
    print("💡 Usage Tips")
    print("="*60)
    
    tips = [
        "🏠 ابدأ من الصفحة الرئيسية لرؤية الإحصائيات",
        "📹 اذهب لصفحة الكاميرات لإدارة الكاميرات",
        "👥 استخدم صفحة المستخدمين لإدارة الحسابات",
        "🚨 راجع صفحة الأحداث لمراقبة النشاط",
        "📊 استخدم API للحصول على البيانات برمجياً",
        "🔄 أعد تحميل الصفحة إذا لم تظهر البيانات",
        "🔐 استخدم admin/admin123 لتسجيل الدخول",
        "⚡ النظام محسن للسرعة والأداء العالي"
    ]
    
    for tip in tips:
        print(f"   {tip}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        return False
    
    # فحص الملفات
    if not check_files():
        print("\n❌ فشل في فحص الملفات")
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # اختبار النظام
    if not test_system():
        print("\n❌ فشل في اختبار النظام")
        return False
    
    # تشغيل النظام
    process = start_system()
    if not process:
        print("\n❌ فشل في تشغيل النظام")
        return False
    
    # فتح المتصفح
    open_browser()
    
    # عرض معلومات النظام
    show_system_info()
    show_usage_tips()
    
    print("\n" + "="*60)
    print("🎉 تم تشغيل النظام بنجاح!")
    print("🎉 System started successfully!")
    print("="*60)
    print("⏹️  اضغط Ctrl+C لإيقاف النظام")
    print("⏹️  Press Ctrl+C to stop the system")
    
    try:
        # انتظار إيقاف النظام
        process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 إيقاف النظام...")
        print("🛑 Stopping system...")
        process.terminate()
        process.wait()
        print("✅ تم إيقاف النظام بنجاح")
        print("✅ System stopped successfully")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في تشغيل النظام")
        print("💡 تحقق من الأخطاء أعلاه وحاول مرة أخرى")
        sys.exit(1)
    else:
        print("\n✅ تم إنهاء النظام بنجاح")
        sys.exit(0)
