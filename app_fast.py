#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للنظام
Fast system startup
"""

import os
import sys

# استخدام التكوين السريع
sys.path.insert(0, '.')
os.environ['FLASK_ENV'] = 'production'

# استيراد التكوين السريع
try:
    from config_fast import Config
    import config
    # استبدال التكوين
    for attr in dir(Config):
        if not attr.startswith('_'):
            setattr(config.Config, attr, getattr(Config, attr))
    print("✅ تم تحميل التكوين السريع")
except ImportError:
    print("⚠️ استخدام التكوين الافتراضي")

# تشغيل التطبيق
if __name__ == '__main__':
    from app import app
    from config import Config
    
    print("🚀 تشغيل النظام في الوضع السريع...")
    print(f"🌐 الرابط: http://{Config.HOST}:{Config.PORT}")
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=False,
        threaded=True,
        processes=1,
        use_reloader=False
    )
