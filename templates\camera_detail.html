{% extends "base.html" %}

{% block title %}{{ camera.name }} - Camera Monitoring System{% endblock %}

{% block content %}
<div class="row">
    <!-- Camera Stream -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-video"></i> {{ camera.name }}
                    <span class="badge bg-{{ 'success' if camera.status == 'online' else 'secondary' }} ms-2">
                        {{ camera.status }}
                    </span>
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshStream()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> Fullscreen
                    </button>
                    {% if current_user.can_manage_cameras() %}
                    <button class="btn btn-outline-warning btn-sm" onclick="editCamera()">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="card-body p-0">
                <div class="camera-detail-container">
                    <img id="cameraStream" 
                         src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                         class="camera-detail-stream" 
                         alt="Camera {{ camera.name }}"
                         onerror="this.src='/static/images/camera-offline.png'">
                    
                    <!-- Stream Controls Overlay -->
                    <div class="stream-controls">
                        <div class="controls-panel">
                            <button class="btn btn-sm btn-dark" onclick="takeSnapshot()">
                                <i class="fas fa-camera"></i> Snapshot
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="toggleRecording()" id="recordBtn">
                                <i class="fas fa-record-vinyl"></i> Record
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Camera Info and Controls -->
    <div class="col-lg-4">
        <!-- Camera Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Camera Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td>{{ camera.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>Type:</strong></td>
                        <td>{{ camera.camera_type.replace('_', ' ').title() }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-{{ 'success' if camera.status == 'online' else 'secondary' }}">
                                {{ camera.status }}
                            </span>
                        </td>
                    </tr>
                    {% if camera.ip_address %}
                    <tr>
                        <td><strong>IP Address:</strong></td>
                        <td>{{ camera.ip_address }}</td>
                    </tr>
                    {% endif %}
                    {% if camera.port %}
                    <tr>
                        <td><strong>Port:</strong></td>
                        <td>{{ camera.port }}</td>
                    </tr>
                    {% endif %}
                    {% if camera.location_name %}
                    <tr>
                        <td><strong>Location:</strong></td>
                        <td>{{ camera.location_name }}</td>
                    </tr>
                    {% endif %}
                    {% if camera.last_seen %}
                    <tr>
                        <td><strong>Last Seen:</strong></td>
                        <td>{{ camera.last_seen }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- Camera Location Map -->
        {% if camera.latitude and camera.longitude %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location</h6>
            </div>
            <div class="card-body p-0">
                <div id="cameraLocationMap" style="height: 200px;"></div>
            </div>
        </div>
        {% endif %}

        <!-- Motion Detection -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-running"></i> Motion Detection</h6>
            </div>
            <div class="card-body">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="motionDetection" checked>
                    <label class="form-check-label" for="motionDetection">
                        Enable Motion Detection
                    </label>
                </div>
                <hr>
                <div class="motion-stats">
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-primary" id="motionToday">0</h6>
                            <small class="text-muted">Today</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-info" id="motionWeek">0</h6>
                            <small class="text-muted">This Week</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="testConnection()">
                        <i class="fas fa-wifi"></i> Test Connection
                    </button>
                    <button class="btn btn-outline-info" onclick="restartStream()">
                        <i class="fas fa-redo"></i> Restart Stream
                    </button>
                    {% if current_user.can_manage_cameras() %}
                    <button class="btn btn-outline-warning" onclick="editCamera()">
                        <i class="fas fa-edit"></i> Edit Settings
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCamera()">
                        <i class="fas fa-trash"></i> Delete Camera
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Back to Dashboard -->
<div class="row mt-4">
    <div class="col-12">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let isRecording = false;
let locationMap;

$(document).ready(function() {
    {% if camera.latitude and camera.longitude %}
    initializeLocationMap();
    {% endif %}
    
    // Load motion detection stats
    loadMotionStats();
});

{% if camera.latitude and camera.longitude %}
function initializeLocationMap() {
    locationMap = L.map('cameraLocationMap').setView([{{ camera.latitude }}, {{ camera.longitude }}], 15);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(locationMap);
    
    // Add camera marker
    const icon = L.divIcon({
        className: 'camera-marker',
        html: '<i class="fas fa-video text-primary"></i>',
        iconSize: [20, 20]
    });
    
    L.marker([{{ camera.latitude }}, {{ camera.longitude }}], {icon: icon})
        .addTo(locationMap)
        .bindPopup('<strong>{{ camera.name }}</strong><br>{{ camera.location_name or "Camera Location" }}');
}
{% endif %}

function refreshStream() {
    const img = document.getElementById('cameraStream');
    const src = img.src;
    img.src = '';
    img.src = src + '?t=' + new Date().getTime();
}

function toggleFullscreen() {
    const container = document.querySelector('.camera-detail-container');
    if (!document.fullscreenElement) {
        container.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function takeSnapshot() {
    // Create a canvas to capture the current frame
    const img = document.getElementById('cameraStream');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    ctx.drawImage(img, 0, 0);
    
    // Download the snapshot
    const link = document.createElement('a');
    link.download = `camera_{{ camera.id }}_${new Date().toISOString()}.png`;
    link.href = canvas.toDataURL();
    link.click();
}

function toggleRecording() {
    const btn = document.getElementById('recordBtn');
    
    if (!isRecording) {
        // Start recording
        isRecording = true;
        btn.innerHTML = '<i class="fas fa-stop"></i> Stop';
        btn.classList.remove('btn-danger');
        btn.classList.add('btn-warning');
        
        // TODO: Implement actual recording functionality
        console.log('Recording started for camera {{ camera.id }}');
    } else {
        // Stop recording
        isRecording = false;
        btn.innerHTML = '<i class="fas fa-record-vinyl"></i> Record';
        btn.classList.remove('btn-warning');
        btn.classList.add('btn-danger');
        
        console.log('Recording stopped for camera {{ camera.id }}');
    }
}

function testConnection() {
    $.post(`/api/cameras/{{ camera.id }}/test`, function(response) {
        if (response.working) {
            alert('Camera connection is working!');
        } else {
            alert('Camera connection failed!');
        }
    }).fail(function() {
        alert('Error testing camera connection');
    });
}

function restartStream() {
    $.post(`/api/cameras/{{ camera.id }}/stop`, function() {
        setTimeout(function() {
            $.post(`/api/cameras/{{ camera.id }}/start`, function() {
                refreshStream();
            });
        }, 2000);
    });
}

function loadMotionStats() {
    // TODO: Implement motion statistics loading
    $('#motionToday').text('5');
    $('#motionWeek').text('23');
}

{% if current_user.can_manage_cameras() %}
function editCamera() {
    // TODO: Implement camera editing modal
    alert('Camera editing functionality coming soon!');
}

function deleteCamera() {
    if (confirm('Are you sure you want to delete this camera?')) {
        // TODO: Implement camera deletion
        alert('Camera deletion functionality coming soon!');
    }
}
{% endif %}
</script>
{% endblock %}
