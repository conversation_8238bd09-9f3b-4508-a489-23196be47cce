# 📺 دليل القنوات للكاميرات

## 🎯 ما هي القناة (Channel)؟

القناة هي رقم يحدد أي كاميرا أو تدفق فيديو تريد الوصول إليه من جهاز واحد يدعم عدة كاميرات.

## 📹 أنواع الكاميرات والقنوات:

### 1. **كاميرات داهوا (Dahua)**
```
القناة: 1-16 (حسب النموذج)
النوع الفرعي: 
  - 0 = التدفق الرئيسي (جودة عالية)
  - 1 = التدفق الفرعي (جودة منخفضة)

مثال RTSP:
rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
rtsp://admin:password@*************:554/cam/realmonitor?channel=2&subtype=1
```

### 2. **كاميرات هيكفيجن (Hikvision)**
```
القناة: 1-32 (حسب النموذج)
تنسيق القناة: (رقم القناة × 100) + 1

أمثلة:
- القناة 1 = 101
- القناة 2 = 201  
- القناة 3 = 301

مثال RTSP:
rtsp://admin:password@*************:554/Streaming/Channels/101
rtsp://admin:password@*************:554/Streaming/Channels/201
```

### 3. **كاميرات IP العامة**
```
القناة: 1-8 (عادة)
تنسيق بسيط: stream + رقم القناة

مثال RTSP:
rtsp://admin:password@*************:554/stream1
rtsp://admin:password@*************:554/stream2
```

### 4. **كاميرات USB**
```
القناة: فهرس الجهاز (0, 1, 2, ...)
لا تحتاج RTSP - تستخدم فهرس الجهاز مباشرة
```

## 🔧 كيفية استخدام القنوات في النظام:

### **إضافة كاميرا جديدة:**
1. اختر نوع الكاميرا
2. أدخل عنوان IP والمنفذ
3. **أدخل رقم القناة** (1-16 عادة)
4. اختر النوع الفرعي:
   - **0** = التدفق الرئيسي (جودة عالية، استهلاك أكبر)
   - **1** = التدفق الفرعي (جودة منخفضة، استهلاك أقل)

### **أمثلة عملية:**

#### **مثال 1: جهاز داهوا بـ 4 كاميرات**
```
الكاميرا 1: القناة=1, النوع=0 (رئيسي)
الكاميرا 2: القناة=2, النوع=0 (رئيسي)  
الكاميرا 3: القناة=3, النوع=1 (فرعي)
الكاميرا 4: القناة=4, النوع=1 (فرعي)
```

#### **مثال 2: جهاز هيكفيجن بـ 8 كاميرات**
```
الكاميرا 1: القناة=1 → RTSP: .../101
الكاميرا 2: القناة=2 → RTSP: .../201
الكاميرا 3: القناة=3 → RTSP: .../301
...
الكاميرا 8: القناة=8 → RTSP: .../801
```

## 💡 نصائح مهمة:

### **1. اختيار النوع الفرعي:**
- **التدفق الرئيسي (0)**: 
  - ✅ جودة عالية (1080p, 4K)
  - ❌ استهلاك شبكة أكبر
  - 🎯 للمراقبة المهمة

- **التدفق الفرعي (1)**:
  - ✅ استهلاك شبكة أقل
  - ❌ جودة أقل (720p, 480p)
  - 🎯 للمراقبة العامة

### **2. اختبار القنوات:**
- ابدأ بالقناة 1
- جرب القنوات 1-4 أولاً
- استخدم برنامج VLC لاختبار RTSP

### **3. استكشاف الأخطاء:**
```bash
# اختبار RTSP بـ VLC
vlc rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0

# اختبار بـ FFmpeg
ffplay rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
```

## 📊 جدول مرجعي سريع:

| نوع الكاميرا | تنسيق القناة | مثال RTSP |
|-------------|-------------|-----------|
| Dahua | channel=X&subtype=Y | `/cam/realmonitor?channel=1&subtype=0` |
| Hikvision | (X×100)+1 | `/Streaming/Channels/101` |
| IP عامة | streamX | `/stream1` |
| USB | فهرس الجهاز | لا يحتاج RTSP |

## 🚀 في النظام:

عند إضافة كاميرا جديدة:
1. **اسم الكاميرا**: "كاميرا الباب الأمامي"
2. **نوع الكاميرا**: "Dahua"
3. **عنوان IP**: "*************"
4. **المنفذ**: "554"
5. **القناة**: "1" ← **جديد!**
6. **النوع الفرعي**: "0" (رئيسي) ← **جديد!**
7. **اسم المستخدم**: "admin"
8. **كلمة المرور**: "password123"

سيقوم النظام تلقائياً ببناء RTSP:
```
rtsp://admin:password123@*************:554/cam/realmonitor?channel=1&subtype=0
```

## ✅ فوائد إضافة القنوات:

1. **دعم أجهزة متعددة القنوات** - جهاز واحد، عدة كاميرات
2. **مرونة في اختيار الجودة** - رئيسي أو فرعي
3. **توفير عرض النطاق** - استخدام التدفق الفرعي عند الحاجة
4. **دعم أفضل للكاميرات التجارية** - داهوا وهيكفيجن
5. **سهولة الإدارة** - تنظيم أفضل للكاميرات

---

**🎉 الآن النظام يدعم القنوات بالكامل! 📺**
