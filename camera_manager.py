import cv2
import numpy as np
import threading
import time
from datetime import datetime
import requests
import imutils
from config import Config
from database import DatabaseManager

# Try to import ONVIF, but continue without it if not available
try:
    from onvif import ONVIFCamera
    ONVIF_AVAILABLE = True
except ImportError:
    ONVIF_AVAILABLE = False
    print("Warning: ONVIF support not available. Install onvif-zeep for ONVIF camera support.")

class CameraStream:
    def __init__(self, camera_id, camera_info, db_manager):
        self.camera_id = camera_id
        self.camera_info = camera_info
        self.db_manager = db_manager
        self.cap = None
        self.frame = None
        self.is_running = False
        self.thread = None
        self.last_motion_time = 0
        self.motion_detector = None
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2()
        
    def start(self):
        """Start the camera stream"""
        if self.is_running:
            return False
            
        self.is_running = True
        self.thread = threading.Thread(target=self._capture_frames)
        self.thread.daemon = True
        self.thread.start()
        return True
    
    def stop(self):
        """Stop the camera stream"""
        self.is_running = False
        if self.thread:
            self.thread.join()
        if self.cap:
            self.cap.release()
    
    def _get_camera_connection(self):
        """Establish connection to camera based on type"""
        camera_type = self.camera_info['camera_type']
        
        try:
            if camera_type == 'usb_camera':
                # USB camera (usually index 0, 1, 2, etc.)
                camera_index = int(self.camera_info.get('ip_address', 0))
                self.cap = cv2.VideoCapture(camera_index)
                
            elif camera_type in ['rtsp_stream', 'ip_camera', 'dahua', 'hikvision']:
                # RTSP or IP camera
                rtsp_url = self.camera_info['rtsp_url']
                if not rtsp_url:
                    # Build RTSP URL if not provided
                    rtsp_url = self._build_rtsp_url()
                
                self.cap = cv2.VideoCapture(rtsp_url)
                
            elif camera_type == 'onvif':
                # ONVIF camera
                self.cap = self._connect_onvif_camera()
                
            else:
                raise ValueError(f"Unsupported camera type: {camera_type}")
            
            if self.cap and self.cap.isOpened():
                # Set camera properties for better performance
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.cap.set(cv2.CAP_PROP_FPS, 30)
                
                # Set resolution based on quality setting
                if Config.STREAM_QUALITY == 'high':
                    self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
                    self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
                elif Config.STREAM_QUALITY == 'medium':
                    self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                    self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                else:  # low quality
                    self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                    self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                
                self.db_manager.update_camera_status(self.camera_id, 'online')
                return True
            else:
                self.db_manager.update_camera_status(self.camera_id, 'offline')
                return False
                
        except Exception as e:
            print(f"Error connecting to camera {self.camera_id}: {str(e)}")
            self.db_manager.update_camera_status(self.camera_id, 'error')
            return False
    
    def _build_rtsp_url(self):
        """Build RTSP URL from camera info"""
        ip = self.camera_info['ip_address']
        port = self.camera_info.get('port', 554)
        username = self.camera_info.get('username', '')
        password = self.camera_info.get('password', '')
        camera_type = self.camera_info['camera_type']
        channel = self.camera_info.get('channel', 1)
        subtype = self.camera_info.get('subtype', 0)

        if camera_type == 'dahua':
            if username and password:
                return f"rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor?channel={channel}&subtype={subtype}"
            else:
                return f"rtsp://{ip}:{port}/cam/realmonitor?channel={channel}&subtype={subtype}"

        elif camera_type == 'hikvision':
            # Hikvision channel format: 101, 201, 301, etc. (channel*100 + 1)
            hik_channel = channel * 100 + 1
            if username and password:
                return f"rtsp://{username}:{password}@{ip}:{port}/Streaming/Channels/{hik_channel:03d}"
            else:
                return f"rtsp://{ip}:{port}/Streaming/Channels/{hik_channel:03d}"

        else:  # generic IP camera
            if username and password:
                return f"rtsp://{username}:{password}@{ip}:{port}/stream{channel}"
            else:
                return f"rtsp://{ip}:{port}/stream{channel}"
    
    def _connect_onvif_camera(self):
        """Connect to ONVIF camera"""
        if not ONVIF_AVAILABLE:
            print("ONVIF support not available. Please install onvif-zeep package.")
            return None

        try:
            ip = self.camera_info['ip_address']
            port = self.camera_info.get('port', 80)
            username = self.camera_info.get('username', 'admin')
            password = self.camera_info.get('password', '')

            # Create ONVIF camera object
            mycam = ONVIFCamera(ip, port, username, password)

            # Get media service
            media_service = mycam.create_media_service()

            # Get profiles
            profiles = media_service.GetProfiles()

            if profiles:
                # Get stream URI from first profile
                profile = profiles[0]
                stream_setup = media_service.create_type('GetStreamUri')
                stream_setup.ProfileToken = profile.token
                stream_setup.StreamSetup = {
                    'Stream': 'RTP-Unicast',
                    'Transport': {'Protocol': 'RTSP'}
                }

                stream_uri = media_service.GetStreamUri(stream_setup)
                return cv2.VideoCapture(stream_uri.Uri)

        except Exception as e:
            print(f"ONVIF connection error: {str(e)}")
            return None
    
    def _capture_frames(self):
        """Capture frames from camera in a separate thread"""
        if not self._get_camera_connection():
            return
        
        while self.is_running:
            try:
                ret, frame = self.cap.read()
                
                if ret:
                    # Resize frame for better performance
                    frame = imutils.resize(frame, width=800)
                    
                    # Apply motion detection
                    self._detect_motion(frame)
                    
                    # Store the frame
                    self.frame = frame.copy()
                    
                    # Update camera status
                    self.db_manager.update_camera_status(self.camera_id, 'online')
                    
                else:
                    # Try to reconnect
                    self.db_manager.update_camera_status(self.camera_id, 'reconnecting')
                    time.sleep(5)
                    if self.cap:
                        self.cap.release()
                    self._get_camera_connection()
                    
            except Exception as e:
                print(f"Error capturing frame from camera {self.camera_id}: {str(e)}")
                self.db_manager.update_camera_status(self.camera_id, 'error')
                time.sleep(5)
        
        if self.cap:
            self.cap.release()
    
    def _detect_motion(self, frame):
        """Detect motion in the frame"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray = cv2.GaussianBlur(gray, (21, 21), 0)
            
            # Apply background subtraction
            fg_mask = self.background_subtractor.apply(gray)
            
            # Find contours
            contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            motion_detected = False
            for contour in contours:
                if cv2.contourArea(contour) > Config.MOTION_MIN_AREA:
                    motion_detected = True
                    break
            
            if motion_detected:
                current_time = time.time()
                if current_time - self.last_motion_time > 5:  # 5 seconds cooldown
                    self.last_motion_time = current_time
                    
                    # Log motion event
                    confidence = min(len(contours) * 10, 100)  # Simple confidence calculation
                    self.db_manager.log_motion_event(self.camera_id, confidence)
                    
                    print(f"Motion detected on camera {self.camera_id}")
                    
        except Exception as e:
            print(f"Motion detection error: {str(e)}")
    
    def get_frame(self):
        """Get the latest frame"""
        return self.frame
    
    def get_jpeg_frame(self):
        """Get frame as JPEG bytes for streaming"""
        if self.frame is not None:
            ret, buffer = cv2.imencode('.jpg', self.frame)
            if ret:
                return buffer.tobytes()
        return None


class CameraManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.active_streams = {}
        self.stream_lock = threading.Lock()
    
    def start_camera_stream(self, camera_id):
        """Start streaming for a specific camera"""
        with self.stream_lock:
            if camera_id in self.active_streams:
                return True
            
            camera_info = self.db_manager.get_camera_by_id(camera_id)
            if not camera_info:
                return False
            
            stream = CameraStream(camera_id, camera_info, self.db_manager)
            if stream.start():
                self.active_streams[camera_id] = stream
                return True
            return False
    
    def stop_camera_stream(self, camera_id):
        """Stop streaming for a specific camera"""
        with self.stream_lock:
            if camera_id in self.active_streams:
                self.active_streams[camera_id].stop()
                del self.active_streams[camera_id]
    
    def get_camera_frame(self, camera_id):
        """Get frame from a specific camera"""
        with self.stream_lock:
            if camera_id not in self.active_streams:
                if not self.start_camera_stream(camera_id):
                    return None
            
            return self.active_streams[camera_id].get_jpeg_frame()
    
    def get_all_active_cameras(self):
        """Get list of all active camera IDs"""
        with self.stream_lock:
            return list(self.active_streams.keys())
    
    def stop_all_streams(self):
        """Stop all camera streams"""
        with self.stream_lock:
            for stream in self.active_streams.values():
                stream.stop()
            self.active_streams.clear()
    
    def test_camera_connection(self, camera_info):
        """Test if camera connection is working"""
        test_stream = CameraStream(0, camera_info, self.db_manager)
        if test_stream._get_camera_connection():
            test_stream.stop()
            return True
        return False

    def start_recording(self, camera_id):
        """Start recording for a camera"""
        try:
            camera = self.db_manager.get_camera_by_id(camera_id)
            if not camera:
                return False

            # Create recordings directory if it doesn't exist
            import os
            from datetime import datetime

            recordings_dir = "recordings"
            if not os.path.exists(recordings_dir):
                os.makedirs(recordings_dir)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{camera_id}_{timestamp}.mp4"
            filepath = os.path.join(recordings_dir, filename)

            # Start recording (simplified implementation)
            # In a real implementation, you would use OpenCV VideoWriter
            print(f"Starting recording for camera {camera_id} to {filepath}")

            # Log the recording start
            self.db_manager.log_system_event('INFO', f'Recording started for camera {camera_id}')

            return True

        except Exception as e:
            print(f"Error starting recording for camera {camera_id}: {str(e)}")
            return False

    def take_snapshot(self, camera_id):
        """Take a snapshot from a camera"""
        try:
            camera = self.db_manager.get_camera_by_id(camera_id)
            if not camera:
                return None

            # Create snapshots directory if it doesn't exist
            import os
            from datetime import datetime

            snapshots_dir = "snapshots"
            if not os.path.exists(snapshots_dir):
                os.makedirs(snapshots_dir)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{camera_id}_{timestamp}.jpg"
            filepath = os.path.join(snapshots_dir, filename)

            # Get current frame
            frame = self.get_camera_frame(camera_id)
            if frame:
                # Save frame as image
                with open(filepath, 'wb') as f:
                    f.write(frame)

                # Log the snapshot
                self.db_manager.log_system_event('INFO', f'Snapshot taken for camera {camera_id}')

                return filepath
            else:
                # Create a placeholder snapshot if camera is offline
                import numpy as np

                # Create a black image with text
                img = np.zeros((480, 640, 3), dtype=np.uint8)

                # Add text
                font = cv2.FONT_HERSHEY_SIMPLEX
                text = f"Camera {camera_id} Offline"
                font_scale = 1
                color = (255, 255, 255)
                thickness = 2

                # Get text size and center it
                text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
                text_x = (img.shape[1] - text_size[0]) // 2
                text_y = (img.shape[0] + text_size[1]) // 2

                cv2.putText(img, text, (text_x, text_y), font, font_scale, color, thickness)

                # Save placeholder image
                cv2.imwrite(filepath, img)

                return filepath

        except Exception as e:
            print(f"Error taking snapshot for camera {camera_id}: {str(e)}")
            return None

    def restart_camera(self, camera_id):
        """Restart a camera stream"""
        try:
            # Stop the camera first
            self.stop_camera_stream(camera_id)

            # Wait a moment
            import time
            time.sleep(1)

            # Start the camera again
            return self.start_camera_stream(camera_id)

        except Exception as e:
            print(f"Error restarting camera {camera_id}: {str(e)}")
            return False
