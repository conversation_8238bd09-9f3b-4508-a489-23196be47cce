#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة وسريعة من نظام مراقبة الكاميرات
Minimal and fast version of camera monitoring system
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, session, Response
import sqlite3
import hashlib
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple-fast-key'
app.config['JSON_AS_ASCII'] = False  # دعم UTF-8
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False  # تحسين الأداء

# إعدادات بسيطة
DATABASE_PATH = 'camera_system.db'

def get_db_connection():
    """اتصال سريع بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def verify_user(username, password):
    """التحقق من المستخدم"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                      (username, password_hash))
        user = cursor.fetchone()
        conn.close()
        return dict(user) if user else None
    except:
        return None

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    html_content = '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام مراقبة الكاميرات - النسخة السريعة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .fast-badge { background: linear-gradient(45deg, #28a745, #20c997); }
            .performance-card { border-left: 4px solid #28a745; }
        </style>
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-video"></i> نظام مراقبة الكاميرات
                    <span class="badge fast-badge ms-2">سريع</span>
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/logout">
                        <i class="fas fa-sign-out-alt"></i> تسجيل خروج
                    </a>
                </div>
            </div>
        </nav>
        
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success performance-card">
                        <h4><i class="fas fa-rocket"></i> النسخة السريعة</h4>
                        <p class="mb-0">⚡ وقت التحميل: أقل من ثانية واحدة | 🚀 محسن للأداء العالي</p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-video fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">الكاميرات</h5>
                            <p class="card-text">عرض وإدارة الكاميرات</p>
                            <a href="/cameras" class="btn btn-primary">
                                <i class="fas fa-eye"></i> عرض الكاميرات
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-cog fa-3x text-success mb-3"></i>
                            <h5 class="card-title">الإدارة</h5>
                            <p class="card-text">إدارة النظام والإعدادات</p>
                            <a href="/admin" class="btn btn-success">
                                <i class="fas fa-tools"></i> لوحة الإدارة
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-info mb-3"></i>
                            <h5 class="card-title">المستخدمون</h5>
                            <p class="card-text">إدارة المستخدمين</p>
                            <a href="/users" class="btn btn-info">
                                <i class="fas fa-user-cog"></i> إدارة المستخدمين
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> إحصائيات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <h3 class="text-primary" id="camera-count">-</h3>
                                    <p>الكاميرات</p>
                                </div>
                                <div class="col-md-3">
                                    <h3 class="text-success" id="user-count">-</h3>
                                    <p>المستخدمون</p>
                                </div>
                                <div class="col-md-3">
                                    <h3 class="text-info">⚡</h3>
                                    <p>الأداء</p>
                                </div>
                                <div class="col-md-3">
                                    <h3 class="text-warning">🚀</h3>
                                    <p>السرعة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // تحميل الإحصائيات
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('camera-count').textContent = data.cameras || 0;
                    document.getElementById('user-count').textContent = data.users || 0;
                })
                .catch(() => {
                    document.getElementById('camera-count').textContent = '0';
                    document.getElementById('user-count').textContent = '0';
                });
        </script>
    </body>
    </html>
    '''

    return Response(html_content, mimetype='text/html; charset=utf-8')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = verify_user(username, password)
        if user:
            session['user_id'] = user['id']
            session['username'] = user['username']
            return redirect(url_for('index'))
        else:
            error = 'اسم المستخدم أو كلمة المرور غير صحيحة'
    else:
        error = None
    
    login_html = f'''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول - نظام مراقبة الكاميرات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-primary">
        <div class="container">
            <div class="row justify-content-center" style="min-height: 100vh; align-items: center;">
                <div class="col-md-6 col-lg-4">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-video fa-3x text-primary"></i>
                                <h3 class="mt-3">نظام مراقبة الكاميرات</h3>
                                <span class="badge bg-success">النسخة السريعة</span>
                            </div>
                            
                            {f'<div class="alert alert-danger">{error}</div>' if error else ''}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </button>
                            </form>
                            
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    المستخدم التجريبي: admin / admin123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

    return Response(login_html, mimetype='text/html; charset=utf-8')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    return redirect(url_for('login'))

@app.route('/cameras')
def cameras():
    """صفحة الكاميرات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return jsonify({
        "message": "صفحة الكاميرات متاحة في النسخة الكاملة",
        "note": "هذه النسخة السريعة للاختبار فقط",
        "cameras": []
    })

@app.route('/admin')
def admin():
    """صفحة الإدارة"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return jsonify({
        "message": "لوحة الإدارة متاحة في النسخة الكاملة",
        "note": "هذه النسخة السريعة للاختبار فقط",
        "features": ["إدارة الكاميرات", "إدارة المستخدمين", "الإعدادات"]
    })

@app.route('/users')
def users():
    """صفحة المستخدمين"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return jsonify({
        "message": "إدارة المستخدمين متاحة في النسخة الكاملة",
        "note": "هذه النسخة السريعة للاختبار فقط",
        "current_user": session.get('username', 'غير معروف')
    })

@app.route('/api/stats')
def api_stats():
    """إحصائيات سريعة"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) as count FROM cameras WHERE is_active = 1')
        camera_count = cursor.fetchone()['count']
        
        cursor.execute('SELECT COUNT(*) as count FROM users WHERE is_active = 1')
        user_count = cursor.fetchone()['count']
        
        conn.close()
        
        return jsonify({
            'cameras': camera_count,
            'users': user_count,
            'status': 'fast',
            'version': 'minimal'
        })
    except:
        return jsonify({
            'cameras': 0,
            'users': 0,
            'status': 'error',
            'version': 'minimal'
        })

if __name__ == '__main__':
    print("🚀 تشغيل النسخة المبسطة والسريعة...")
    print("⚡ هذه النسخة محسنة للسرعة القصوى")
    print("🌐 الرابط: http://127.0.0.1:4040")
    print("🔑 تسجيل الدخول: admin / admin123")
    print("=" * 50)
    
    app.run(
        host='127.0.0.1',  # localhost فقط للسرعة
        port=4040,
        debug=False,       # إيقاف debug
        threaded=True,     # تفعيل threading
        use_reloader=False # إيقاف auto-reload
    )
