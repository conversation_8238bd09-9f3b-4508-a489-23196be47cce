# 📷 Python Multi-Camera Monitoring System

A comprehensive web-based camera surveillance system built with Python Flask that supports multiple camera types including Dahua, Hikvision, IP cameras, USB cameras, and RTSP streams.

## 🌟 Features

### Core Functionality
- **Multi-Camera Support**: Dahua, Hikvision, IP Camera, USB Camera, RTSP Stream, ONVIF
- **Real-time Streaming**: Live video feeds using OpenCV and Flask
- **Interactive Map**: Camera locations displayed on an interactive map
- **User Authentication**: Role-based access (<PERSON><PERSON>, Manager, Viewer)
- **Motion Detection**: Automatic motion detection with alerts
- **Recording**: Video recording capabilities
- **Responsive Design**: Works on desktop and mobile devices

### Advanced Features
- **ONVIF Protocol Support**: Connect to ONVIF-compatible cameras
- **Motion Alerts**: Email and WhatsApp notifications
- **System Monitoring**: Health monitoring and statistics
- **Auto-cleanup**: Automatic cleanup of old recordings
- **Snapshot Capture**: Take snapshots from live streams
- **Fullscreen Mode**: Full-screen camera viewing

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project files
cd camera-monitoring-system

# Install required packages
pip install -r requirements.txt
```

### 2. Configuration

Edit `config.py` to customize your settings:

```python
# Server configuration
HOST = '0.0.0.0'
PORT = 4040

# Camera settings
STREAM_QUALITY = 'medium'  # low, medium, high
MAX_CAMERAS = 50

# Map center (your location)
DEFAULT_MAP_CENTER = [40.7128, -74.0060]  # New York coordinates
```

### 3. Run the Application

```bash
python app.py
```

Visit: **http://localhost:4040**

### 4. Default Login

- **Username**: `admin`
- **Password**: `admin123`

## 📋 System Requirements

- Python 3.7+
- OpenCV 4.x
- Flask 2.x
- SQLite (included)
- Modern web browser

## 🎯 Supported Camera Types

### 1. Dahua Cameras
```
Type: dahua
RTSP URL: rtsp://username:password@ip:554/cam/realmonitor?channel=1&subtype=0
```

### 2. Hikvision Cameras
```
Type: hikvision
RTSP URL: rtsp://username:password@ip:554/Streaming/Channels/101
```

### 3. Generic IP Cameras
```
Type: ip_camera
RTSP URL: rtsp://username:password@ip:554/stream1
```

### 4. USB Cameras
```
Type: usb_camera
Device Index: 0, 1, 2, etc.
```

### 5. RTSP Streams
```
Type: rtsp_stream
Custom RTSP URL: rtsp://your-stream-url
```

### 6. ONVIF Cameras
```
Type: onvif
IP Address: Camera IP
Port: 80 (default)
Username/Password: Camera credentials
```

## 🔧 Configuration Guide

### Adding Cameras

1. **Login** as Manager or Admin
2. **Click "Add Camera"** button
3. **Fill in camera details**:
   - Name: Descriptive name
   - Type: Select camera type
   - IP Address: Camera IP
   - Port: Camera port (usually 554 for RTSP)
   - Username/Password: Camera credentials
   - Location: GPS coordinates and description

### Camera Types Configuration

#### For Dahua Cameras:
- IP Address: `*************`
- Port: `554`
- Username: `admin`
- Password: `your_password`

#### For Hikvision Cameras:
- IP Address: `*************`
- Port: `554`
- Username: `admin`
- Password: `your_password`

#### For USB Cameras:
- IP Address: `0` (device index)
- Leave other fields empty

#### For Custom RTSP:
- Fill in complete RTSP URL in the RTSP URL field

## 🗺️ Map Integration

The system uses OpenStreetMap (Leaflet) to display camera locations:

1. **Add GPS coordinates** when creating cameras
2. **Click camera markers** on the map to view live feed
3. **Customize map center** in `config.py`

## 🔐 User Management

### User Roles:
- **Viewer (1)**: Can only view camera feeds
- **Manager (2)**: Can add/edit cameras and view feeds
- **Admin (3)**: Full system access

### Creating Users:
```python
# In Python console or add to database.py
db_manager = DatabaseManager()
db_manager.create_user('username', 'password', role=2, email='<EMAIL>')
```

## 📧 Notifications Setup

### Email Notifications:
Edit `config.py`:
```python
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your_app_password'
```

### WhatsApp Notifications:
Configure WhatsApp API key in `config.py`:
```python
WHATSAPP_API_KEY = 'your_whatsapp_api_key'
```

## 🎮 Usage Guide

### Dashboard Features:
- **Live Camera Grid**: View all cameras at once
- **Camera Status**: Online/Offline indicators
- **Interactive Map**: Click markers to view cameras
- **System Stats**: Camera counts and status
- **Quick Actions**: Refresh, add cameras

### Individual Camera View:
- **Full-screen Mode**: Press F11 or click fullscreen button
- **Take Snapshot**: Capture current frame
- **Start/Stop Recording**: Record video
- **Motion Detection**: Enable/disable motion alerts

### Admin Panel:
- **Camera Management**: Add, edit, delete cameras
- **System Monitoring**: View system health
- **User Management**: Manage user accounts
- **System Logs**: View system activity

## 🔧 Troubleshooting

### Common Issues:

#### Camera Not Connecting:
1. Check IP address and port
2. Verify username/password
3. Test camera URL in VLC player
4. Check network connectivity
5. Verify camera supports RTSP

#### Poor Video Quality:
1. Adjust `STREAM_QUALITY` in config.py
2. Check network bandwidth
3. Reduce number of simultaneous streams

#### Motion Detection Not Working:
1. Check camera lighting conditions
2. Adjust `MOTION_THRESHOLD` in config.py
3. Verify motion detection is enabled

#### Performance Issues:
1. Reduce number of active cameras
2. Lower stream quality
3. Check system resources (CPU/Memory)

## 📁 Project Structure

```
camera-monitoring-system/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── database.py           # Database management
├── camera_manager.py     # Camera handling
├── auth.py              # Authentication system
├── utils.py             # Utility functions
├── requirements.txt     # Python dependencies
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── login.html
│   ├── admin.html
│   └── camera_detail.html
├── static/             # Static files
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
└── recordings/         # Video recordings (auto-created)
```

## 🔒 Security Notes

1. **Change default admin password** immediately
2. **Use HTTPS** in production
3. **Configure firewall** properly
4. **Regular security updates**
5. **Strong camera passwords**

## 🚀 Production Deployment

### Using Gunicorn:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:4040 app:app
```

### Using Docker:
```dockerfile
FROM python:3.9
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 4040
CMD ["python", "app.py"]
```

## 📝 API Endpoints

- `GET /api/cameras` - Get all cameras
- `POST /api/cameras` - Add new camera
- `POST /api/cameras/{id}/test` - Test camera connection
- `POST /api/cameras/{id}/start` - Start camera stream
- `POST /api/cameras/{id}/stop` - Stop camera stream
- `GET /video_feed/{id}` - Live video stream

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

For support and questions:
1. Check the troubleshooting section
2. Review camera documentation
3. Test with VLC player first
4. Check system logs in admin panel

---

**Happy Monitoring! 📹🔍**
