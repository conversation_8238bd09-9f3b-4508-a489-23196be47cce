import os
from datetime import timedelta

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # Database configuration
    DATABASE_PATH = 'camera_system.db'
    
    # Server configuration - سريع
    HOST = '0.0.0.0'
    PORT = 4040
    DEBUG = False
    THREADED = True
    
    # Camera configuration - سريع
    CAMERA_TIMEOUT = 10
    STREAM_QUALITY = 'low'  # جودة منخفضة للسرعة
    MAX_CAMERAS = 10
    FRAME_RATE = 10
    BUFFER_SIZE = 1
    
    # Recording configuration
    RECORDING_PATH = 'recordings'
    MAX_RECORDING_DAYS = 3
    
    # Motion detection - مبسط
    MOTION_THRESHOLD = 50
    MOTION_MIN_AREA = 2000
    MOTION_DETECTION_INTERVAL = 5  # كل 5 ثوان
    
    # Performance settings - سريع
    DATABASE_POOL_SIZE = 5
    MAX_CONCURRENT_STREAMS = 3
    CACHE_TIMEOUT = 600
    ENABLE_COMPRESSION = True
    
    # Map configuration
    DEFAULT_MAP_CENTER = [40.7128, -74.0060]
    DEFAULT_ZOOM = 10
    
    # User session
    PERMANENT_SESSION_LIFETIME = timedelta(hours=6)
    
    # Logging - أقل
    LOG_LEVEL = 'ERROR'
    MAX_LOG_SIZE = 512 * 1024  # 512KB
