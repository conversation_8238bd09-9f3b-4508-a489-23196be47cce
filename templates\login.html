<!DOCTYPE html>
<html lang="{{ g.lang }}" dir="{{ 'rtl' if g.is_rtl else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ g.get_text('login') }} - {{ g.get_text('camera_monitoring_system') }}</title>
    
    <!-- Bootstrap CSS -->
    {% if g.is_rtl %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Arabic Font -->
    {% if g.lang == 'ar' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    {% endif %}
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: {{ "'Cairo', " if g.lang == 'ar' else "" }}'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h3><i class="fas fa-video"></i> {{ g.get_text('camera_monitor') }}</h3>
                        <p class="mb-0">{{ g.get_text('multi_camera_surveillance') }}</p>

                        <!-- Language Selector -->
                        <div class="mt-3">
                            <div class="btn-group btn-group-sm">
                                <a href="?language=en" class="btn btn-outline-light {{ 'active' if g.lang == 'en' else '' }}">English</a>
                                <a href="?language=ar" class="btn btn-outline-light {{ 'active' if g.lang == 'ar' else '' }}">العربية</a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> {{ g.get_text('username') }}
                                </label>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="{{ g.get_text('enter_username') }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> {{ g.get_text('password') }}
                                </label>
                                <input type="password" class="form-control" id="password" name="password"
                                       placeholder="{{ g.get_text('enter_password') }}" required>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> {{ g.get_text('login') }}
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <strong>{{ g.get_text('default_login') }}:</strong><br>
                                {{ g.get_text('username') }}: admin<br>
                                {{ g.get_text('password') }}: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
