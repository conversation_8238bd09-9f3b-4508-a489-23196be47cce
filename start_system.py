#!/usr/bin/env python3
"""
Camera Monitoring System Startup Script
"""

import os
import sys
import subprocess
import time
from config import Config

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        'flask', 'opencv-python', 'numpy', 'imutils', 
        'flask-login', 'werkzeug', 'pillow', 'requests',
        'python-dotenv', 'flask-wtf', 'wtforms', 'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_optional_dependencies():
    """Check optional dependencies"""
    optional_packages = {
        'onvif-zeep': 'ONVIF camera support',
        'aiohttp': 'Async HTTP support'
    }
    
    print("\n📦 Optional packages:")
    for package, description in optional_packages.items():
        try:
            if package == 'onvif-zeep':
                import onvif
            else:
                __import__(package.replace('-', '_'))
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ⚠️  {package} - {description} (not installed)")

def create_directories():
    """Create necessary directories"""
    directories = [
        Config.RECORDING_PATH,
        'static/images',
        'logs'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

def show_startup_info():
    """Show startup information"""
    print("\n" + "="*60)
    print("🎥 CAMERA MONITORING SYSTEM")
    print("="*60)
    print(f"🌐 Starting web server on: http://{Config.HOST}:{Config.PORT}")
    print(f"📊 Debug mode: {'ON' if Config.DEBUG else 'OFF'}")
    print(f"📁 Database: {Config.DATABASE_PATH}")
    print(f"📹 Recordings: {Config.RECORDING_PATH}")
    print(f"🎯 Max cameras: {Config.MAX_CAMERAS}")
    print(f"📺 Stream quality: {Config.STREAM_QUALITY}")
    print("\n🔑 Default login credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("\n💡 Tips:")
    print("   • Press Ctrl+C to stop the server")
    print("   • Run demo_setup.py to add sample cameras")
    print("   • Check the admin panel for system monitoring")
    print("="*60)

def main():
    """Main startup function"""
    print("🚀 Camera Monitoring System - Startup")
    print("Checking system requirements...\n")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check optional dependencies
    check_optional_dependencies()
    
    # Create directories
    print("\n📁 Setting up directories...")
    create_directories()
    
    # Show startup info
    show_startup_info()
    
    # Ask if user wants to run demo setup
    if not os.path.exists(Config.DATABASE_PATH):
        print("\n🎯 First time setup detected!")
        response = input("Would you like to add demo cameras? (y/n): ")
        if response.lower() in ['y', 'yes']:
            try:
                subprocess.run([sys.executable, 'demo_setup.py'], check=True)
            except subprocess.CalledProcessError:
                print("⚠️  Demo setup failed, but you can run it manually later.")
    
    print("\n🚀 Starting Camera Monitoring System...")
    print("   (This may take a few seconds...)")
    
    try:
        # Import and run the Flask app
        from app import app
        app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG, threaded=True)
    except KeyboardInterrupt:
        print("\n\n👋 Camera Monitoring System stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting system: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
