# -*- coding: utf-8 -*-
"""
نظام الترجمة متعدد اللغات
Multi-language translation system
"""

translations = {
    'ar': {
        # Navigation
        'camera_monitor': 'مراقب الكاميرات',
        'dashboard': 'لوحة التحكم',
        'admin': 'الإدارة',
        'logout': 'تسجيل الخروج',
        'login': 'تسجيل الدخول',
        
        # User roles
        'admin_role': 'مدير',
        'manager_role': 'مشرف',
        'viewer_role': 'مشاهد',
        
        # Dashboard
        'live_cameras': 'الكاميرات المباشرة',
        'refresh_all': 'تحديث الكل',
        'add_camera': 'إضافة كاميرا',
        'camera_locations': 'مواقع الكاميرات',
        'system_status': 'حالة النظام',
        'online': 'متصل',
        'offline': 'غير متصل',
        'total': 'المجموع',
        'recent_activity': 'النشاط الأخير',
        'no_recent_activity': 'لا يوجد نشاط حديث',
        'no_cameras_configured': 'لم يتم تكوين أي كاميرات',
        'add_first_camera': 'أضف أول كاميرا لبدء المراقبة',
        
        # Camera management
        'camera_name': 'اسم الكاميرا',
        'camera_type': 'نوع الكاميرا',
        'ip_address': 'عنوان IP',
        'port': 'المنفذ',
        'username': 'اسم المستخدم',
        'password': 'كلمة المرور',
        'rtsp_url': 'رابط RTSP',
        'latitude': 'خط العرض',
        'longitude': 'خط الطول',
        'location_name': 'اسم الموقع',
        'status': 'الحالة',
        'last_seen': 'آخر ظهور',
        'actions': 'الإجراءات',
        
        # Camera types
        'dahua': 'داهوا',
        'hikvision': 'هيكفيجن',
        'ip_camera': 'كاميرا IP',
        'usb_camera': 'كاميرا USB',
        'rtsp_stream': 'بث RTSP',
        'onvif': 'ONVIF',
        
        # Camera actions
        'view': 'عرض',
        'edit': 'تعديل',
        'delete': 'حذف',
        'test': 'اختبار',
        'start': 'تشغيل',
        'stop': 'إيقاف',
        'refresh': 'تحديث',
        'fullscreen': 'ملء الشاشة',
        'snapshot': 'لقطة',
        'record': 'تسجيل',
        
        # Forms
        'required': 'مطلوب',
        'optional': 'اختياري',
        'select_type': 'اختر النوع...',
        'cancel': 'إلغاء',
        'save': 'حفظ',
        'add': 'إضافة',
        'update': 'تحديث',
        
        # Camera detail
        'camera_information': 'معلومات الكاميرا',
        'location': 'الموقع',
        'motion_detection': 'كشف الحركة',
        'enable_motion_detection': 'تفعيل كشف الحركة',
        'today': 'اليوم',
        'this_week': 'هذا الأسبوع',
        'quick_actions': 'إجراءات سريعة',
        'test_connection': 'اختبار الاتصال',
        'restart_stream': 'إعادة تشغيل البث',
        'edit_settings': 'تعديل الإعدادات',
        'delete_camera': 'حذف الكاميرا',
        'back_to_dashboard': 'العودة للوحة التحكم',
        
        # Admin panel
        'admin_panel': 'لوحة الإدارة',
        'total_cameras': 'إجمالي الكاميرات',
        'errors': 'أخطاء',
        'camera_management': 'إدارة الكاميرات',
        'system_logs': 'سجلات النظام',
        'loading_system_logs': 'جاري تحميل سجلات النظام...',
        'inactive': 'غير نشط',
        'not_set': 'غير محدد',
        'never': 'أبداً',
        
        # Status messages
        'online_status': 'متصل',
        'offline_status': 'غير متصل',
        'error_status': 'خطأ',
        'reconnecting_status': 'إعادة الاتصال',
        
        # Notifications
        'camera_started': 'تم تشغيل الكاميرا بنجاح',
        'camera_stopped': 'تم إيقاف الكاميرا',
        'camera_connection_working': 'اتصال الكاميرا يعمل!',
        'camera_connection_failed': 'فشل اتصال الكاميرا!',
        'error_testing_connection': 'خطأ في اختبار الاتصال',
        'refreshing_cameras': 'جاري تحديث الكاميرات...',
        'camera_added_successfully': 'تم إضافة الكاميرا بنجاح',
        'error_adding_camera': 'خطأ في إضافة الكاميرا',
        'copied_to_clipboard': 'تم النسخ للحافظة',
        'failed_to_copy': 'فشل في النسخ للحافظة',
        
        # Login page
        'multi_camera_surveillance': 'نظام مراقبة متعدد الكاميرات',
        'default_login': 'بيانات الدخول الافتراضية',
        'invalid_credentials': 'اسم المستخدم أو كلمة المرور غير صحيحة.',
        'logged_out': 'تم تسجيل الخروج.',
        'no_permission': 'ليس لديك صلاحية للوصول لهذه الصفحة.',
        'unauthorized': 'غير مخول',
        'access_denied': 'تم رفض الوصول. ليس لديك صلاحية لتنفيذ هذا الإجراء.',
        'role': 'الدور',
        
        # Error messages
        'camera_not_found': 'الكاميرا غير موجودة.',
        'missing_required_field': 'حقل مطلوب مفقود',
        'invalid_ip_format': 'تنسيق عنوان IP غير صحيح',
        'port_range_error': 'المنفذ يجب أن يكون بين 1 و 65535',
        'server_error': 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
        'resource_not_found': 'المورد غير موجود.',
        'an_error_occurred': 'حدث خطأ',
        
        # Motion detection
        'motion_detected': 'تم اكتشاف حركة',
        'motion_alert': 'تنبيه حركة',
        'motion_events': 'أحداث الحركة',
        'confidence': 'الثقة',
        'detection_time': 'وقت الاكتشاف',
        
        # Recording
        'start_recording': 'بدء التسجيل',
        'stop_recording': 'إيقاف التسجيل',
        'recording_started': 'بدأ التسجيل',
        'recording_stopped': 'توقف التسجيل',
        'recordings': 'التسجيلات',
        'duration': 'المدة',
        'file_size': 'حجم الملف',
        
        # System info
        'system_uptime': 'وقت تشغيل النظام',
        'memory_usage': 'استخدام الذاكرة',
        'cpu_usage': 'استخدام المعالج',
        'disk_usage': 'استخدام القرص',
        'system_health': 'صحة النظام',
        'performance': 'الأداء',
        
        # Time and dates
        'seconds': 'ثانية',
        'minutes': 'دقيقة',
        'hours': 'ساعة',
        'days': 'يوم',
        'weeks': 'أسبوع',
        'months': 'شهر',
        'years': 'سنة',
        
        # File operations
        'download': 'تحميل',
        'upload': 'رفع',
        'export': 'تصدير',
        'import': 'استيراد',
        'backup': 'نسخ احتياطي',
        'restore': 'استعادة',
        
        # Confirmation messages
        'confirm_delete': 'هل أنت متأكد من حذف هذه الكاميرا؟',
        'confirm_restart': 'هل تريد إعادة تشغيل النظام؟',
        'confirm_logout': 'هل تريد تسجيل الخروج؟',
        'operation_successful': 'تمت العملية بنجاح',
        'operation_failed': 'فشلت العملية',
        
        # Help and tips
        'tips': 'نصائح',
        'help': 'مساعدة',
        'documentation': 'التوثيق',
        'support': 'الدعم',
        'about': 'حول',
        'version': 'الإصدار',
        'contact': 'اتصل بنا',
        
        # Placeholders
        'enter_camera_name': 'أدخل اسم الكاميرا',
        'enter_ip_address': 'أدخل عنوان IP',
        'enter_username': 'أدخل اسم المستخدم',
        'enter_password': 'أدخل كلمة المرور',
        'enter_location': 'أدخل الموقع',
        'example_ip': '*************',
        'example_location': 'الباب الأمامي، موقف السيارات، إلخ.',

        # Additional UI elements
        'id': 'المعرف',
        'name': 'الاسم',
        'type': 'النوع',
        'created_at': 'تاريخ الإنشاء',
        'updated_at': 'تاريخ التحديث',
        'description': 'الوصف',
        'settings': 'الإعدادات',
        'profile': 'الملف الشخصي',
        'preferences': 'التفضيلات',
        'language': 'اللغة',
        'theme': 'المظهر',
        'notifications': 'الإشعارات',
        'security': 'الأمان',
        'privacy': 'الخصوصية',
        'advanced': 'متقدم',
        'basic': 'أساسي',
        'custom': 'مخصص',
        'default': 'افتراضي',
        'enabled': 'مفعل',
        'disabled': 'معطل',
        'active': 'نشط',
        'inactive': 'غير نشط',
        'loading': 'جاري التحميل',
        'saving': 'جاري الحفظ',
        'processing': 'جاري المعالجة',
        'connecting': 'جاري الاتصال',
        'disconnected': 'منقطع',
        'connected': 'متصل',
        'failed': 'فشل',
        'success': 'نجح',
        'warning': 'تحذير',
        'info': 'معلومات',
        'error': 'خطأ',
        'debug': 'تصحيح',
        'trace': 'تتبع',

        # Camera status descriptions
        'camera_offline_desc': 'الكاميرا غير متصلة',
        'camera_online_desc': 'الكاميرا تعمل بشكل طبيعي',
        'camera_error_desc': 'خطأ في الكاميرا',
        'camera_reconnecting_desc': 'جاري إعادة الاتصال بالكاميرا',
        
        # Footer
        'all_rights_reserved': 'جميع الحقوق محفوظة',
        'camera_monitoring_system': 'نظام مراقبة الكاميرات'
    },
    
    'en': {
        # English translations (default)
        'camera_monitor': 'Camera Monitor',
        'dashboard': 'Dashboard',
        'admin': 'Admin',
        'logout': 'Logout',
        'login': 'Login',
        'admin_role': 'Admin',
        'manager_role': 'Manager',
        'viewer_role': 'Viewer',
        'live_cameras': 'Live Cameras',
        'refresh_all': 'Refresh All',
        'add_camera': 'Add Camera',
        'camera_locations': 'Camera Locations',
        'system_status': 'System Status',
        'online': 'Online',
        'offline': 'Offline',
        'total': 'Total',
        'recent_activity': 'Recent Activity',
        'no_recent_activity': 'No recent activity',
        'no_cameras_configured': 'No cameras configured',
        'add_first_camera': 'Add your first camera to start monitoring',
        # ... (rest of English translations)
    }
}

def get_text(key, lang='en'):
    """Get translated text for a given key and language"""
    return translations.get(lang, {}).get(key, translations['en'].get(key, key))

def get_user_language(request):
    """Detect user language from request headers or session"""
    from flask import session

    # Check URL parameter first
    if 'language' in request.args:
        lang = request.args.get('language')
        session['language'] = lang  # Save to session
        return lang

    # Check session
    if 'language' in session:
        return session['language']

    # Check Accept-Language header
    if request.headers.get('Accept-Language'):
        accept_lang = request.headers.get('Accept-Language')
        if 'ar' in accept_lang:
            return 'ar'

    return 'en'  # Default to English

def is_rtl_language(lang):
    """Check if language is right-to-left"""
    rtl_languages = ['ar', 'he', 'fa', 'ur']
    return lang in rtl_languages
