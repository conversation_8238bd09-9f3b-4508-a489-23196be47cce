import sqlite3
import hashlib
from datetime import datetime
from config import Config

class DatabaseManager:
    def __init__(self, db_path=None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role INTEGER NOT NULL DEFAULT 1,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Cameras table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                camera_type TEXT NOT NULL,
                rtsp_url TEXT,
                ip_address TEXT,
                port INTEGER,
                username TEXT,
                password TEXT,
                latitude REAL,
                longitude REAL,
                location_name TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP,
                status TEXT DEFAULT 'offline'
            )
        ''')
        
        # Camera groups table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Camera group assignments
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera_group_assignments (
                camera_id INTEGER,
                group_id INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id),
                FOREIGN KEY (group_id) REFERENCES camera_groups (id),
                PRIMARY KEY (camera_id, group_id)
            )
        ''')
        
        # Motion events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motion_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                confidence REAL,
                image_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # System logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        
        # Create default admin user if no users exist
        cursor.execute('SELECT COUNT(*) FROM users')
        if cursor.fetchone()[0] == 0:
            self.create_user('admin', 'admin123', Config.USER_ROLES['admin'], '<EMAIL>')
        
        conn.close()
    
    def create_user(self, username, password, role=1, email=None):
        """Create a new user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        try:
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, email)
                VALUES (?, ?, ?, ?)
            ''', (username, password_hash, role, email))
            conn.commit()
            user_id = cursor.lastrowid
            conn.close()
            return user_id
        except sqlite3.IntegrityError:
            conn.close()
            return None
    
    def verify_user(self, username, password):
        """Verify user credentials"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        cursor.execute('''
            SELECT id, username, role, email FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            # Update last login
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user['id'],))
            conn.commit()
        
        conn.close()
        return dict(user) if user else None
    
    def get_user_by_id(self, user_id):
        """Get user by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role, email, created_at, last_login 
            FROM users WHERE id = ? AND is_active = 1
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        return dict(user) if user else None
    
    def add_camera(self, name, camera_type, rtsp_url=None, ip_address=None, 
                   port=None, username=None, password=None, latitude=None, 
                   longitude=None, location_name=None):
        """Add a new camera"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO cameras (name, camera_type, rtsp_url, ip_address, port,
                               username, password, latitude, longitude, location_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, camera_type, rtsp_url, ip_address, port, username, 
              password, latitude, longitude, location_name))
        
        conn.commit()
        camera_id = cursor.lastrowid
        conn.close()
        return camera_id
    
    def get_cameras(self, active_only=True):
        """Get all cameras"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = 'SELECT * FROM cameras'
        if active_only:
            query += ' WHERE is_active = 1'
        query += ' ORDER BY name'
        
        cursor.execute(query)
        cameras = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return cameras
    
    def get_camera_by_id(self, camera_id):
        """Get camera by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM cameras WHERE id = ?', (camera_id,))
        camera = cursor.fetchone()
        conn.close()
        return dict(camera) if camera else None
    
    def update_camera_status(self, camera_id, status):
        """Update camera status"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE cameras SET status = ?, last_seen = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (status, camera_id))
        
        conn.commit()
        conn.close()
    
    def log_motion_event(self, camera_id, confidence, image_path=None):
        """Log a motion detection event"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO motion_events (camera_id, confidence, image_path)
            VALUES (?, ?, ?)
        ''', (camera_id, confidence, image_path))
        
        conn.commit()
        conn.close()
    
    def log_system_event(self, level, message, user_id=None):
        """Log a system event"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO system_logs (level, message, user_id)
            VALUES (?, ?, ?)
        ''', (level, message, user_id))
        
        conn.commit()
        conn.close()
