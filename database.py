import sqlite3
import hashlib
from datetime import datetime
from config import Config

class DatabaseManager:
    def __init__(self, db_path=None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role INTEGER NOT NULL DEFAULT 1,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Cameras table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                camera_type TEXT NOT NULL,
                rtsp_url TEXT,
                ip_address TEXT,
                port INTEGER,
                username TEXT,
                password TEXT,
                channel INTEGER DEFAULT 1,
                subtype INTEGER DEFAULT 0,
                latitude REAL,
                longitude REAL,
                location_name TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP,
                status TEXT DEFAULT 'offline'
            )
        ''')
        
        # Camera groups table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Camera group assignments
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS camera_group_assignments (
                camera_id INTEGER,
                group_id INTEGER,
                FOREIGN KEY (camera_id) REFERENCES cameras (id),
                FOREIGN KEY (group_id) REFERENCES camera_groups (id),
                PRIMARY KEY (camera_id, group_id)
            )
        ''')
        
        # Motion events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motion_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                confidence REAL,
                image_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # System logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        
        # Create default admin user if no users exist
        cursor.execute('SELECT COUNT(*) FROM users')
        if cursor.fetchone()[0] == 0:
            self.create_user('admin', 'admin123', Config.USER_ROLES['admin'], '<EMAIL>')
        
        conn.close()
    
    def create_user(self, username, password, role=1, email=None):
        """Create a new user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        try:
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, email)
                VALUES (?, ?, ?, ?)
            ''', (username, password_hash, role, email))
            conn.commit()
            user_id = cursor.lastrowid
            conn.close()
            return user_id
        except sqlite3.IntegrityError:
            conn.close()
            return None
    
    def verify_user(self, username, password):
        """Verify user credentials"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        cursor.execute('''
            SELECT id, username, role, email FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            # Update last login
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user['id'],))
            conn.commit()
        
        conn.close()
        return dict(user) if user else None
    
    def get_user_by_id(self, user_id):
        """Get user by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role, email, created_at, last_login 
            FROM users WHERE id = ? AND is_active = 1
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        return dict(user) if user else None
    
    def add_camera(self, name, camera_type, rtsp_url=None, ip_address=None,
                   port=None, username=None, password=None, channel=1, subtype=0,
                   latitude=None, longitude=None, location_name=None):
        """Add a new camera"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO cameras (name, camera_type, rtsp_url, ip_address, port,
                               username, password, channel, subtype, latitude, longitude, location_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, camera_type, rtsp_url, ip_address, port, username,
              password, channel, subtype, latitude, longitude, location_name))

        conn.commit()
        camera_id = cursor.lastrowid
        conn.close()
        return camera_id
    
    def get_cameras(self, active_only=True):
        """Get all cameras"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = 'SELECT * FROM cameras'
        if active_only:
            query += ' WHERE is_active = 1'
        query += ' ORDER BY name'
        
        cursor.execute(query)
        cameras = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return cameras
    
    def get_camera_by_id(self, camera_id):
        """Get camera by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM cameras WHERE id = ?', (camera_id,))
        camera = cursor.fetchone()
        conn.close()
        return dict(camera) if camera else None
    
    def update_camera_status(self, camera_id, status):
        """Update camera status"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE cameras SET status = ?, last_seen = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (status, camera_id))
        
        conn.commit()
        conn.close()
    
    def log_motion_event(self, camera_id, confidence, image_path=None):
        """Log a motion detection event"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO motion_events (camera_id, confidence, image_path)
            VALUES (?, ?, ?)
        ''', (camera_id, confidence, image_path))
        
        conn.commit()
        conn.close()
    
    def log_system_event(self, level, message, user_id=None):
        """Log a system event"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO system_logs (level, message, user_id)
            VALUES (?, ?, ?)
        ''', (level, message, user_id))

        conn.commit()
        conn.close()

    # User Management Functions
    def get_all_users(self):
        """Get all users"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, username, role, email, is_active, created_at, last_login
            FROM users ORDER BY created_at DESC
        ''')

        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return users

    def update_user(self, user_id, username=None, email=None, role=None, is_active=None):
        """Update user information"""
        conn = self.get_connection()
        cursor = conn.cursor()

        updates = []
        params = []

        if username is not None:
            updates.append('username = ?')
            params.append(username)
        if email is not None:
            updates.append('email = ?')
            params.append(email)
        if role is not None:
            updates.append('role = ?')
            params.append(role)
        if is_active is not None:
            updates.append('is_active = ?')
            params.append(is_active)

        if updates:
            params.append(user_id)
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()

        conn.close()
        return cursor.rowcount > 0

    def delete_user(self, user_id):
        """Delete user (soft delete by setting is_active to False)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE users SET is_active = 0 WHERE id = ?', (user_id,))
        conn.commit()

        deleted = cursor.rowcount > 0
        conn.close()
        return deleted

    def change_user_password(self, user_id, new_password_hash):
        """Change user password"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE users SET password_hash = ? WHERE id = ?',
                      (new_password_hash, user_id))
        conn.commit()

        updated = cursor.rowcount > 0
        conn.close()
        return updated

    def update_last_login(self, user_id):
        """Update user's last login time"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
                      (user_id,))
        conn.commit()
        conn.close()

    def get_user_stats(self):
        """Get user statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Total users
        cursor.execute('SELECT COUNT(*) as total FROM users WHERE is_active = 1')
        total = cursor.fetchone()['total']

        # Users by role
        cursor.execute('''
            SELECT role, COUNT(*) as count
            FROM users WHERE is_active = 1
            GROUP BY role
        ''')
        by_role = {row['role']: row['count'] for row in cursor.fetchall()}

        # Recent logins (last 7 days)
        cursor.execute('''
            SELECT COUNT(*) as recent
            FROM users
            WHERE is_active = 1 AND last_login >= datetime('now', '-7 days')
        ''')
        recent_logins = cursor.fetchone()['recent']

        conn.close()

        return {
            'total': total,
            'by_role': by_role,
            'recent_logins': recent_logins
        }

    # Camera Management Functions
    def update_camera(self, camera_id, name=None, camera_type=None, ip_address=None,
                     port=None, username=None, password=None, channel=None, subtype=None,
                     latitude=None, longitude=None, location_name=None):
        """Update camera information"""
        conn = self.get_connection()
        cursor = conn.cursor()

        updates = []
        params = []

        if name is not None:
            updates.append('name = ?')
            params.append(name)
        if camera_type is not None:
            updates.append('camera_type = ?')
            params.append(camera_type)
        if ip_address is not None:
            updates.append('ip_address = ?')
            params.append(ip_address)
        if port is not None:
            updates.append('port = ?')
            params.append(port)
        if username is not None:
            updates.append('username = ?')
            params.append(username)
        if password is not None:
            updates.append('password = ?')
            params.append(password)
        if channel is not None:
            updates.append('channel = ?')
            params.append(channel)
        if subtype is not None:
            updates.append('subtype = ?')
            params.append(subtype)
        if latitude is not None:
            updates.append('latitude = ?')
            params.append(latitude)
        if longitude is not None:
            updates.append('longitude = ?')
            params.append(longitude)
        if location_name is not None:
            updates.append('location_name = ?')
            params.append(location_name)

        if updates:
            params.append(camera_id)
            query = f"UPDATE cameras SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()

        conn.close()
        return cursor.rowcount > 0

    def delete_camera(self, camera_id):
        """Delete camera (soft delete by setting is_active to False)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE cameras SET is_active = 0 WHERE id = ?', (camera_id,))
        conn.commit()

        deleted = cursor.rowcount > 0
        conn.close()
        return deleted

    def toggle_camera_active(self, camera_id, active):
        """Toggle camera active status"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE cameras SET is_active = ? WHERE id = ?', (active, camera_id))
        conn.commit()

        updated = cursor.rowcount > 0
        conn.close()
        return updated

    def get_camera_stats(self):
        """Get camera statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Total cameras
        cursor.execute('SELECT COUNT(*) as total FROM cameras WHERE is_active = 1')
        total = cursor.fetchone()['total']

        # Cameras by status
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM cameras WHERE is_active = 1
            GROUP BY status
        ''')
        by_status = {row['status']: row['count'] for row in cursor.fetchall()}

        # Cameras by type
        cursor.execute('''
            SELECT camera_type, COUNT(*) as count
            FROM cameras WHERE is_active = 1
            GROUP BY camera_type
        ''')
        by_type = {row['camera_type']: row['count'] for row in cursor.fetchall()}

        conn.close()

        return {
            'total': total,
            'by_status': by_status,
            'by_type': by_type
        }
