/* Camera Monitoring System Styles */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

/* Camera Grid Styles */
.camera-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.camera-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.camera-stream-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #000;
}

.camera-stream {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-stream-container:hover .camera-overlay {
    opacity: 1;
}

.camera-controls {
    display: flex;
    gap: 10px;
}

/* Camera Detail Page */
.camera-detail-container {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.camera-detail-stream {
    width: 100%;
    height: auto;
    max-height: 600px;
    object-fit: contain;
    display: block;
}

.stream-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-detail-container:hover .stream-controls {
    opacity: 1;
}

.controls-panel {
    display: flex;
    gap: 10px;
    background: rgba(0,0,0,0.7);
    padding: 10px;
    border-radius: 5px;
}

/* Map Styles */
.camera-marker {
    background: white;
    border-radius: 50%;
    width: 30px !important;
    height: 30px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.camera-marker i {
    font-size: 14px;
}

/* Status Badges */
.badge {
    font-size: 0.75em;
}

/* Statistics Cards */
.stat-item {
    padding: 10px;
}

.stat-item h4 {
    margin-bottom: 5px;
    font-weight: bold;
}

/* Motion Detection */
.motion-stats {
    margin-top: 15px;
}

/* Admin Panel */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* System Logs */
#systemLogs {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-stream-container {
        height: 150px;
    }
    
    .camera-detail-stream {
        max-height: 300px;
    }
    
    .controls-panel {
        flex-direction: column;
        gap: 5px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 2px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Fullscreen Styles */
.camera-detail-container:fullscreen {
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-detail-container:fullscreen .camera-detail-stream {
    max-height: 100vh;
    max-width: 100vw;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Button Enhancements */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn:hover {
    transform: none;
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* Utility Classes */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .card-header {
        background-color: #2d3748;
        border-bottom-color: #4a5568;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e9ecef;
        border-color: #4a5568;
    }
}
