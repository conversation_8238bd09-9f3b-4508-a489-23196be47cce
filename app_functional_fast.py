#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة وظيفية سريعة من نظام مراقبة الكاميرات
Functional fast version of camera monitoring system
"""

from flask import Flask, jsonify, request, redirect, session, Response, render_template_string
import sqlite3
import hashlib
import os
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'functional-fast-key'
app.config['JSON_AS_ASCII'] = False

DATABASE_PATH = 'camera_system.db'

def get_db_connection():
    """اتصال سريع بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def verify_user(username, password):
    """التحقق من المستخدم"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('SELECT * FROM users WHERE username = ? AND password_hash = ?', 
                      (username, password_hash))
        user = cursor.fetchone()
        conn.close()
        return dict(user) if user else None
    except:
        return None

def require_login():
    """التحقق من تسجيل الدخول"""
    if 'user_id' not in session:
        return False
    return True

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if not require_login():
        return redirect('/login')
    
    html = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة الكاميرات - وظيفي سريع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .fast-gradient { background: linear-gradient(45deg, #007bff, #28a745); color: white; }
        .card-hover:hover { transform: translateY(-5px); transition: 0.3s; }
        .stat-card { border-left: 4px solid #007bff; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark fast-gradient">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-video"></i> نظام مراقبة الكاميرات
                <span class="badge bg-light text-dark ms-2">وظيفي سريع</span>
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، """ + session.get('username', 'مستخدم') + """</span>
                <a class="nav-link" href="/logout">
                    <i class="fas fa-sign-out-alt"></i> خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success stat-card">
                    <h4><i class="fas fa-rocket"></i> النسخة الوظيفية السريعة</h4>
                    <p class="mb-0">⚡ سريع ووظيفي | 🛠️ جميع المميزات متاحة | 📊 إحصائيات حية</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card card-hover text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-video fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">الكاميرات</h5>
                        <p class="text-muted">عدد الكاميرات: <span id="camera-count" class="fw-bold text-primary">-</span></p>
                        <a href="/cameras" class="btn btn-primary">
                            <i class="fas fa-eye"></i> عرض الكاميرات
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card card-hover text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-cog fa-3x text-success mb-3"></i>
                        <h5 class="card-title">الإدارة</h5>
                        <p class="text-muted">إدارة شاملة للنظام</p>
                        <a href="/admin" class="btn btn-success">
                            <i class="fas fa-tools"></i> لوحة الإدارة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card card-hover text-center h-100">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-info mb-3"></i>
                        <h5 class="card-title">المستخدمون</h5>
                        <p class="text-muted">عدد المستخدمين: <span id="user-count" class="fw-bold text-info">-</span></p>
                        <a href="/users" class="btn btn-info">
                            <i class="fas fa-user-cog"></i> إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header fast-gradient">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> إحصائيات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <h3 class="text-primary" id="cameras-online">-</h3>
                                    <small>كاميرات متصلة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <h3 class="text-success" id="active-users">-</h3>
                                    <small>مستخدمين نشطين</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <h3 class="text-warning" id="motion-events">-</h3>
                                    <small>أحداث حركة اليوم</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <h3 class="text-info">⚡</h3>
                                    <small>حالة النظام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> آخر الأنشطة</h5>
                    </div>
                    <div class="card-body">
                        <div id="recent-activities">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل الإحصائيات
        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('camera-count').textContent = data.cameras || 0;
                    document.getElementById('user-count').textContent = data.users || 0;
                    document.getElementById('cameras-online').textContent = data.cameras_online || 0;
                    document.getElementById('active-users').textContent = data.active_users || 0;
                    document.getElementById('motion-events').textContent = data.motion_events || 0;
                })
                .catch(() => {
                    document.getElementById('camera-count').textContent = '0';
                    document.getElementById('user-count').textContent = '0';
                });
        }
        
        // تحميل الأنشطة الأخيرة
        function loadActivities() {
            fetch('/api/activities')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('recent-activities');
                    if (data.activities && data.activities.length > 0) {
                        container.innerHTML = data.activities.map(activity => 
                            `<div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                <div>
                                    <i class="fas fa-${activity.icon} text-${activity.color}"></i>
                                    <span class="ms-2">${activity.message}</span>
                                </div>
                                <small class="text-muted">${activity.time}</small>
                            </div>`
                        ).join('');
                    } else {
                        container.innerHTML = '<p class="text-muted text-center">لا توجد أنشطة حديثة</p>';
                    }
                })
                .catch(() => {
                    document.getElementById('recent-activities').innerHTML = 
                        '<p class="text-danger text-center">خطأ في تحميل الأنشطة</p>';
                });
        }
        
        // تحميل البيانات عند بدء الصفحة
        loadStats();
        loadActivities();
        
        // تحديث البيانات كل 30 ثانية
        setInterval(loadStats, 30000);
        setInterval(loadActivities, 60000);
    </script>
</body>
</html>"""
    
    return Response(html, mimetype='text/html; charset=utf-8')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = verify_user(username, password)
        if user:
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            return redirect('/')
        else:
            error = 'اسم المستخدم أو كلمة المرور غير صحيحة'
    else:
        error = None
    
    error_html = f'<div class="alert alert-danger">{error}</div>' if error else ''
    
    html = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول - نظام مراقبة الكاميرات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-bg {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }}
        .login-card {{ box-shadow: 0 15px 35px rgba(0,0,0,0.1); border-radius: 15px; }}
    </style>
</head>
<body class="login-bg">
    <div class="container">
        <div class="row justify-content-center" style="min-height: 100vh; align-items: center;">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-video fa-3x text-primary"></i>
                            <h3 class="mt-3">نظام مراقبة الكاميرات</h3>
                            <span class="badge bg-success">وظيفي سريع</span>
                        </div>
                        
                        {error_html}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user"></i> اسم المستخدم
                                </label>
                                <input type="text" class="form-control" name="username" required 
                                       placeholder="أدخل اسم المستخدم">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-lock"></i> كلمة المرور
                                </label>
                                <input type="password" class="form-control" name="password" required
                                       placeholder="أدخل كلمة المرور">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </button>
                        </form>
                        
                        <div class="text-center mt-4">
                            <div class="alert alert-info">
                                <strong>بيانات تجريبية:</strong><br>
                                المستخدم: <code>admin</code><br>
                                كلمة المرور: <code>admin123</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""
    
    return Response(html, mimetype='text/html; charset=utf-8')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    return redirect('/login')

@app.route('/cameras')
def cameras():
    """صفحة الكاميرات"""
    if not require_login():
        return redirect('/login')
    
    return redirect('/admin?tab=cameras')

@app.route('/users')
def users():
    """صفحة المستخدمين"""
    if not require_login():
        return redirect('/login')
    
    return redirect('/admin?tab=users')

@app.route('/admin')
def admin():
    """لوحة الإدارة"""
    if not require_login():
        return redirect('/login')

    # إعادة توجيه للنسخة الكاملة
    return redirect('http://127.0.0.1:4041/admin?language=ar')

@app.route('/api/stats')
def api_stats():
    """إحصائيات النظام"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # عدد الكاميرات
        cursor.execute('SELECT COUNT(*) as count FROM cameras WHERE is_active = 1')
        camera_count = cursor.fetchone()['count']
        
        # عدد المستخدمين
        cursor.execute('SELECT COUNT(*) as count FROM users WHERE is_active = 1')
        user_count = cursor.fetchone()['count']
        
        # الكاميرات المتصلة
        cursor.execute("SELECT COUNT(*) as count FROM cameras WHERE is_active = 1 AND status = 'online'")
        cameras_online = cursor.fetchone()['count']
        
        # المستخدمين النشطين
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE is_active = 1 AND last_login > datetime('now', '-24 hours')")
        active_users = cursor.fetchone()['count']
        
        # أحداث الحركة اليوم
        cursor.execute("SELECT COUNT(*) as count FROM motion_events WHERE timestamp > datetime('now', '-1 day')")
        motion_events = cursor.fetchone()['count']
        
        conn.close()
        
        return jsonify({
            'cameras': camera_count,
            'users': user_count,
            'cameras_online': cameras_online,
            'active_users': active_users,
            'motion_events': motion_events,
            'status': 'functional-fast',
            'version': 'functional'
        })
    except Exception as e:
        return jsonify({
            'cameras': 0,
            'users': 0,
            'cameras_online': 0,
            'active_users': 0,
            'motion_events': 0,
            'status': 'error',
            'error': str(e)
        })

@app.route('/api/activities')
def api_activities():
    """آخر الأنشطة"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # آخر الأنشطة من سجل النظام
        cursor.execute("""
            SELECT level, message, timestamp 
            FROM system_logs 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        logs = cursor.fetchall()
        
        activities = []
        for log in logs:
            icon = 'info-circle'
            color = 'info'
            
            if log['level'] == 'ERROR':
                icon = 'exclamation-triangle'
                color = 'danger'
            elif log['level'] == 'WARNING':
                icon = 'exclamation-circle'
                color = 'warning'
            elif log['level'] == 'INFO':
                icon = 'info-circle'
                color = 'info'
            
            activities.append({
                'message': log['message'],
                'time': log['timestamp'],
                'icon': icon,
                'color': color
            })
        
        conn.close()
        
        return jsonify({
            'activities': activities,
            'status': 'success'
        })
    except Exception as e:
        return jsonify({
            'activities': [
                {
                    'message': 'النظام يعمل بشكل طبيعي',
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'icon': 'check-circle',
                    'color': 'success'
                }
            ],
            'status': 'default'
        })

if __name__ == '__main__':
    print("🚀 تشغيل النسخة الوظيفية السريعة...")
    print("⚡ سريع ووظيفي مع جميع المميزات")
    print("🌐 http://127.0.0.1:4040")
    print("🔑 admin / admin123")
    print("=" * 50)
    
    app.run(
        host='127.0.0.1',
        port=4040,
        debug=False,
        threaded=True,
        use_reloader=False
    )
