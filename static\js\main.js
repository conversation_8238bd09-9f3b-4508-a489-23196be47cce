// Camera Monitoring System JavaScript

// Global variables
let refreshInterval;
let notificationPermission = false;

// Initialize the application
$(document).ready(function() {
    initializeApp();
});

function initializeApp() {
    // Request notification permission
    requestNotificationPermission();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Set up auto-refresh for camera streams
    setupAutoRefresh();
    
    // Initialize keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Add loading states to buttons
    setupButtonLoading();
}

function requestNotificationPermission() {
    if ('Notification' in window) {
        Notification.requestPermission().then(function(permission) {
            notificationPermission = permission === 'granted';
        });
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupAutoRefresh() {
    // Auto-refresh camera streams every 5 minutes
    refreshInterval = setInterval(function() {
        refreshCameraStreams();
    }, 300000); // 5 minutes
}

function setupKeyboardShortcuts() {
    $(document).keydown(function(e) {
        // Ctrl+R: Refresh all cameras
        if (e.ctrlKey && e.keyCode === 82) {
            e.preventDefault();
            refreshAllCameras();
        }
        
        // Escape: Close modals
        if (e.keyCode === 27) {
            $('.modal').modal('hide');
        }
        
        // F11: Toggle fullscreen for camera detail
        if (e.keyCode === 122 && $('.camera-detail-stream').length) {
            e.preventDefault();
            toggleFullscreen();
        }
    });
}

function setupButtonLoading() {
    // Add loading state to buttons with data-loading attribute
    $('[data-loading]').click(function() {
        const btn = $(this);
        const originalText = btn.html();
        const loadingText = btn.data('loading') || 'Loading...';
        
        btn.html(`<span class="spinner-border spinner-border-sm me-2"></span>${loadingText}`);
        btn.prop('disabled', true);
        
        // Reset after 5 seconds (fallback)
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
        }, 5000);
    });
}

// Camera Management Functions
function refreshCameraStreams() {
    $('.camera-stream').each(function() {
        const img = $(this);
        const src = img.attr('src');
        if (src && src.includes('video_feed')) {
            img.attr('src', src.split('?')[0] + '?t=' + new Date().getTime());
        }
    });
}

function refreshAllCameras() {
    showNotification('Refreshing all cameras...', 'info');
    refreshCameraStreams();
    
    // Reload camera status
    if (typeof updateCameraStats === 'function') {
        updateCameraStats();
    }
}

function startAllCameras() {
    $('.camera-card').each(function() {
        const cameraId = $(this).data('camera-id');
        if (cameraId) {
            startCameraStream(cameraId);
        }
    });
}

function stopAllCameras() {
    $('.camera-card').each(function() {
        const cameraId = $(this).data('camera-id');
        if (cameraId) {
            stopCameraStream(cameraId);
        }
    });
}

function startCameraStream(cameraId) {
    $.post(`/api/cameras/${cameraId}/start`)
        .done(function(response) {
            if (response.success) {
                updateCameraStatus(cameraId, 'online');
                showNotification(`Camera ${cameraId} started successfully`, 'success');
            }
        })
        .fail(function() {
            showNotification(`Failed to start camera ${cameraId}`, 'error');
        });
}

function stopCameraStream(cameraId) {
    $.post(`/api/cameras/${cameraId}/stop`)
        .done(function(response) {
            if (response.success) {
                updateCameraStatus(cameraId, 'offline');
                showNotification(`Camera ${cameraId} stopped`, 'info');
            }
        })
        .fail(function() {
            showNotification(`Failed to stop camera ${cameraId}`, 'error');
        });
}

function updateCameraStatus(cameraId, status) {
    const card = $(`.camera-card[data-camera-id="${cameraId}"]`);
    const badge = card.find('.badge');
    
    // Update badge
    badge.removeClass('bg-success bg-warning bg-danger bg-secondary');
    
    switch(status) {
        case 'online':
            badge.addClass('bg-success');
            break;
        case 'offline':
            badge.addClass('bg-warning');
            break;
        case 'error':
            badge.addClass('bg-danger');
            break;
        default:
            badge.addClass('bg-secondary');
    }
    
    badge.text(status);
}

// Notification Functions
function showNotification(message, type = 'info', duration = 3000) {
    // Show browser notification if permission granted
    if (notificationPermission && type === 'error') {
        new Notification('Camera System Alert', {
            body: message,
            icon: '/static/images/camera-icon.png'
        });
    }
    
    // Show toast notification
    showToast(message, type, duration);
}

function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = getOrCreateToastContainer();
    const toastId = 'toast-' + Date.now();
    
    const bgClass = {
        'success': 'bg-success',
        'error': 'bg-danger',
        'warning': 'bg-warning',
        'info': 'bg-info'
    }[type] || 'bg-info';
    
    const toast = $(`
        <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `);
    
    toastContainer.append(toast);
    
    const bsToast = new bootstrap.Toast(toast[0], {
        autohide: true,
        delay: duration
    });
    
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

function getOrCreateToastContainer() {
    let container = $('#toast-container');
    if (container.length === 0) {
        container = $(`
            <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
            </div>
        `);
        $('body').append(container);
    }
    return container;
}

// Utility Functions
function formatDateTime(dateString) {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    return date.toLocaleString();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Copied to clipboard', 'success', 1000);
    }).catch(function() {
        showNotification('Failed to copy to clipboard', 'error');
    });
}

function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Form Validation
function validateCameraForm(formData) {
    const errors = [];
    
    if (!formData.name || formData.name.trim() === '') {
        errors.push('Camera name is required');
    }
    
    if (!formData.camera_type) {
        errors.push('Camera type is required');
    }
    
    if (formData.camera_type !== 'usb_camera' && !formData.ip_address) {
        errors.push('IP address is required for network cameras');
    }
    
    if (formData.ip_address && !isValidIP(formData.ip_address)) {
        errors.push('Invalid IP address format');
    }
    
    if (formData.port && (formData.port < 1 || formData.port > 65535)) {
        errors.push('Port must be between 1 and 65535');
    }
    
    return errors;
}

function isValidIP(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

// Error Handling
function handleAjaxError(xhr, textStatus, errorThrown) {
    let message = 'An error occurred';
    
    if (xhr.responseJSON && xhr.responseJSON.error) {
        message = xhr.responseJSON.error;
    } else if (xhr.status === 401) {
        message = 'Unauthorized. Please log in again.';
        window.location.href = '/login';
        return;
    } else if (xhr.status === 403) {
        message = 'Access denied. You do not have permission to perform this action.';
    } else if (xhr.status === 404) {
        message = 'Resource not found.';
    } else if (xhr.status >= 500) {
        message = 'Server error. Please try again later.';
    }
    
    showNotification(message, 'error');
}

// Set up global AJAX error handler
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    handleAjaxError(xhr, settings, thrownError);
});

// Performance Monitoring
function logPerformance(action, startTime) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (duration > 1000) { // Log slow operations (>1 second)
        console.warn(`Slow operation detected: ${action} took ${duration.toFixed(2)}ms`);
    }
}

// Cleanup on page unload
$(window).on('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});

// Export functions for global use
window.CameraSystem = {
    refreshAllCameras,
    startAllCameras,
    stopAllCameras,
    showNotification,
    copyToClipboard,
    downloadFile,
    validateCameraForm
};
