#!/usr/bin/env python3
"""
Demo Setup Script for Camera Monitoring System
This script adds sample cameras for demonstration purposes
"""

from database import DatabaseManager
from config import Config

def setup_demo_cameras():
    """Add demo cameras to the system"""
    db_manager = DatabaseManager()
    
    # Demo cameras data
    demo_cameras = [
        {
            'name': 'Front Door Camera',
            'camera_type': 'ip_camera',
            'ip_address': '*************',
            'port': 554,
            'username': 'admin',
            'password': 'password123',
            'latitude': 40.7128,
            'longitude': -74.0060,
            'location_name': 'Main Entrance'
        },
        {
            'name': 'Parking Lot Camera',
            'camera_type': 'hikvision',
            'ip_address': '*************',
            'port': 554,
            'username': 'admin',
            'password': 'password123',
            'latitude': 40.7130,
            'longitude': -74.0058,
            'location_name': 'Parking Area'
        },
        {
            'name': 'Back Yard Camera',
            'camera_type': 'dahua',
            'ip_address': '*************',
            'port': 554,
            'username': 'admin',
            'password': 'password123',
            'latitude': 40.7126,
            'longitude': -74.0062,
            'location_name': 'Back Yard'
        },
        {
            'name': 'USB Webcam',
            'camera_type': 'usb_camera',
            'ip_address': '0',  # Device index
            'port': None,
            'username': None,
            'password': None,
            'latitude': 40.7128,
            'longitude': -74.0060,
            'location_name': 'Office Desk'
        },
        {
            'name': 'RTSP Test Stream',
            'camera_type': 'rtsp_stream',
            'rtsp_url': 'rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mov',
            'ip_address': None,
            'port': None,
            'username': None,
            'password': None,
            'latitude': 40.7132,
            'longitude': -74.0056,
            'location_name': 'Demo Stream'
        }
    ]
    
    print("Setting up demo cameras...")
    
    for camera in demo_cameras:
        try:
            camera_id = db_manager.add_camera(**camera)
            print(f"✓ Added camera: {camera['name']} (ID: {camera_id})")
        except Exception as e:
            print(f"✗ Failed to add camera {camera['name']}: {str(e)}")
    
    print("\nDemo setup complete!")
    print("\nDemo cameras added:")
    print("1. Front Door Camera (IP Camera)")
    print("2. Parking Lot Camera (Hikvision)")
    print("3. Back Yard Camera (Dahua)")
    print("4. USB Webcam (USB Camera)")
    print("5. RTSP Test Stream (Public test stream)")
    print("\nNote: The IP cameras (1-3) are examples and won't work unless you have actual cameras at those IP addresses.")
    print("The USB webcam will work if you have a webcam connected.")
    print("The RTSP test stream should work if you have internet connectivity.")

def create_demo_users():
    """Create demo users with different roles"""
    db_manager = DatabaseManager()
    
    demo_users = [
        {'username': 'viewer', 'password': 'viewer123', 'role': Config.USER_ROLES['viewer'], 'email': '<EMAIL>'},
        {'username': 'manager', 'password': 'manager123', 'role': Config.USER_ROLES['manager'], 'email': '<EMAIL>'},
    ]
    
    print("\nCreating demo users...")
    
    for user in demo_users:
        try:
            user_id = db_manager.create_user(**user)
            if user_id:
                print(f"✓ Created user: {user['username']} (Role: {user['role']})")
            else:
                print(f"✗ User {user['username']} already exists")
        except Exception as e:
            print(f"✗ Failed to create user {user['username']}: {str(e)}")
    
    print("\nDemo users created:")
    print("- Username: viewer, Password: viewer123 (Viewer role)")
    print("- Username: manager, Password: manager123 (Manager role)")
    print("- Username: admin, Password: admin123 (Admin role - already exists)")

def show_system_info():
    """Show system information"""
    print("\n" + "="*60)
    print("🎥 CAMERA MONITORING SYSTEM - DEMO SETUP COMPLETE")
    print("="*60)
    print(f"🌐 Web Interface: http://localhost:{Config.PORT}")
    print(f"📁 Database: {Config.DATABASE_PATH}")
    print(f"📹 Recordings: {Config.RECORDING_PATH}")
    print("\n📋 LOGIN CREDENTIALS:")
    print("   Admin:   admin / admin123")
    print("   Manager: manager / manager123")
    print("   Viewer:  viewer / viewer123")
    print("\n🎯 FEATURES TO TEST:")
    print("   ✓ Live camera streaming")
    print("   ✓ Interactive map with camera locations")
    print("   ✓ Motion detection")
    print("   ✓ User role management")
    print("   ✓ Camera management (add/edit/delete)")
    print("   ✓ System monitoring")
    print("\n📝 NOTES:")
    print("   • IP cameras are examples - replace with real camera IPs")
    print("   • USB camera will work if webcam is connected")
    print("   • RTSP test stream requires internet connection")
    print("   • Check admin panel for system logs and statistics")
    print("="*60)

if __name__ == "__main__":
    print("🚀 Camera Monitoring System - Demo Setup")
    print("This script will add demo cameras and users to your system.\n")
    
    response = input("Do you want to proceed with demo setup? (y/n): ")
    
    if response.lower() in ['y', 'yes']:
        setup_demo_cameras()
        create_demo_users()
        show_system_info()
    else:
        print("Demo setup cancelled.")
        print(f"You can still access the system at: http://localhost:{Config.PORT}")
        print("Default login: admin / admin123")
