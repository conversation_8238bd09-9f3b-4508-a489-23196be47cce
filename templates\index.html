{% extends "base.html" %}

{% block title %}{{ g.get_text('dashboard') }} - {{ g.get_text('camera_monitoring_system') }}{% endblock %}

{% block content %}
<div class="row">
    <!-- Camera Grid -->
    <div class="col-lg-8">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-video"></i> {{ g.get_text('live_cameras') }}</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshAllCameras()">
                    <i class="fas fa-sync-alt"></i> {{ g.get_text('refresh_all') }}
                </button>
                {% if current_user.can_manage_cameras() %}
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                    <i class="fas fa-plus"></i> {{ g.get_text('add_camera') }}
                </button>
                {% endif %}
            </div>
        </div>

        <div class="row" id="cameraGrid">
            {% for camera in cameras %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card camera-card" data-camera-id="{{ camera.id }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ camera.name }}</h6>
                        <span class="badge bg-{{ 'success' if camera.status == 'online' else 'secondary' }}">
                            {% if camera.status == 'online' %}{{ g.get_text('online') }}
                            {% elif camera.status == 'offline' %}{{ g.get_text('offline') }}
                            {% elif camera.status == 'error' %}{{ g.get_text('error') }}
                            {% else %}{{ camera.status }}{% endif %}
                        </span>
                    </div>
                    <div class="card-body p-0">
                        <div class="camera-stream-container">
                            <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                                 class="camera-stream" 
                                 alt="Camera {{ camera.name }}"
                                 onerror="this.src='/static/images/camera-offline.png'">
                            <div class="camera-overlay">
                                <div class="camera-controls">
                                    <button class="btn btn-sm btn-light" onclick="viewCamera({{ camera.id }})">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    {% if current_user.can_manage_cameras() %}
                                    <button class="btn btn-sm btn-warning" onclick="editCamera({{ camera.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt"></i>
                            {{ camera.location_name or g.get_text('not_set') }}
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
            
            {% if not cameras %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">{{ g.get_text('no_cameras_configured') }}</h4>
                    <p class="text-muted">{{ g.get_text('add_first_camera') }}</p>
                    {% if current_user.can_manage_cameras() %}
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                        <i class="fas fa-plus"></i> {{ g.get_text('add_camera') }}
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Map and Info Panel -->
    <div class="col-lg-4">
        <!-- Camera Map -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-map"></i> {{ g.get_text('camera_locations') }}</h6>
            </div>
            <div class="card-body p-0">
                <div id="cameraMap" style="height: 300px;"></div>
            </div>
        </div>

        <!-- System Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> {{ g.get_text('system_status') }}</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-success" id="onlineCameras">0</h4>
                            <small class="text-muted">{{ g.get_text('online') }}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-warning" id="offlineCameras">0</h4>
                            <small class="text-muted">{{ g.get_text('offline') }}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-primary" id="totalCameras">{{ cameras|length }}</h4>
                            <small class="text-muted">{{ g.get_text('total') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-clock"></i> {{ g.get_text('recent_activity') }}</h6>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <p class="text-muted">{{ g.get_text('no_recent_activity') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Camera Modal -->
{% if current_user.can_manage_cameras() %}
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> {{ g.get_text('add_camera') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">{{ g.get_text('camera_name') }} *</label>
                                <input type="text" class="form-control" id="cameraName"
                                       placeholder="{{ g.get_text('enter_camera_name') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">{{ g.get_text('camera_type') }} *</label>
                                <select class="form-select" id="cameraType" required>
                                    <option value="">{{ g.get_text('select_type') }}</option>
                                    <option value="dahua">{{ g.get_text('dahua') }}</option>
                                    <option value="hikvision">{{ g.get_text('hikvision') }}</option>
                                    <option value="ip_camera">{{ g.get_text('ip_camera') }}</option>
                                    <option value="usb_camera">{{ g.get_text('usb_camera') }}</option>
                                    <option value="rtsp_stream">{{ g.get_text('rtsp_stream') }}</option>
                                    <option value="onvif">{{ g.get_text('onvif') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ipAddress" class="form-label">{{ g.get_text('ip_address') }}</label>
                                <input type="text" class="form-control" id="ipAddress"
                                       placeholder="{{ g.get_text('example_ip') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="port" class="form-label">{{ g.get_text('port') }}</label>
                                <input type="number" class="form-control" id="port" placeholder="554">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">{{ g.get_text('username') }}</label>
                                <input type="text" class="form-control" id="username"
                                       placeholder="{{ g.get_text('enter_username') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">{{ g.get_text('password') }}</label>
                                <input type="password" class="form-control" id="password"
                                       placeholder="{{ g.get_text('enter_password') }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel" class="form-label">{{ g.get_text('channel') }}</label>
                                <input type="number" class="form-control" id="channel"
                                       placeholder="1" value="1" min="1" max="16">
                                <small class="form-text text-muted">{{ g.get_text('channel_help') }}</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subtype" class="form-label">{{ g.get_text('subtype') }}</label>
                                <select class="form-select" id="subtype">
                                    <option value="0">0 - {{ g.get_text('main_stream') if g.get_text('main_stream') != 'main_stream' else 'رئيسي' }}</option>
                                    <option value="1">1 - {{ g.get_text('sub_stream') if g.get_text('sub_stream') != 'sub_stream' else 'فرعي' }}</option>
                                </select>
                                <small class="form-text text-muted">{{ g.get_text('subtype_help') }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rtspUrl" class="form-label">{{ g.get_text('rtsp_url') }} ({{ g.get_text('optional') }})</label>
                        <input type="text" class="form-control" id="rtspUrl"
                               placeholder="rtsp://username:password@ip:port/stream">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">{{ g.get_text('latitude') }}</label>
                                <input type="number" step="any" class="form-control" id="latitude" placeholder="40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">{{ g.get_text('longitude') }}</label>
                                <input type="number" step="any" class="form-control" id="longitude" placeholder="-74.0060">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="locationName" class="form-label">{{ g.get_text('location_name') }}</label>
                        <input type="text" class="form-control" id="locationName"
                               placeholder="{{ g.get_text('example_location') }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ g.get_text('cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">{{ g.get_text('add_camera') }}</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize camera map
let cameraMap;
let cameraMarkers = [];

$(document).ready(function() {
    initializeMap();
    updateCameraStats();
    
    // Refresh camera stats every 30 seconds
    setInterval(updateCameraStats, 30000);
});

function initializeMap() {
    cameraMap = L.map('cameraMap').setView([40.7128, -74.0060], 10);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(cameraMap);
    
    // Add camera markers
    {% for camera in cameras %}
    {% if camera.latitude and camera.longitude %}
    addCameraMarker({{ camera.id }}, {{ camera.latitude }}, {{ camera.longitude }}, '{{ camera.name }}', '{{ camera.status }}');
    {% endif %}
    {% endfor %}
}

function addCameraMarker(cameraId, lat, lng, name, status) {
    const icon = L.divIcon({
        className: 'camera-marker',
        html: `<i class="fas fa-video" style="color: ${status === 'online' ? '#28a745' : '#6c757d'}"></i>`,
        iconSize: [20, 20]
    });
    
    const marker = L.marker([lat, lng], {icon: icon})
        .addTo(cameraMap)
        .bindPopup(`<strong>${name}</strong><br>Status: ${status}`)
        .on('click', function() {
            viewCamera(cameraId);
        });
    
    cameraMarkers.push(marker);
}

function updateCameraStats() {
    $.get('/api/cameras', function(cameras) {
        let online = 0, offline = 0;
        
        cameras.forEach(camera => {
            if (camera.status === 'online') online++;
            else offline++;
        });
        
        $('#onlineCameras').text(online);
        $('#offlineCameras').text(offline);
        $('#totalCameras').text(cameras.length);
    });
}

function viewCamera(cameraId) {
    window.location.href = `/camera/${cameraId}`;
}

function refreshAllCameras() {
    location.reload();
}

{% if current_user.can_manage_cameras() %}
function addCamera() {
    const formData = {
        name: $('#cameraName').val(),
        camera_type: $('#cameraType').val(),
        ip_address: $('#ipAddress').val(),
        port: $('#port').val() ? parseInt($('#port').val()) : null,
        username: $('#username').val(),
        password: $('#password').val(),
        channel: $('#channel').val() ? parseInt($('#channel').val()) : 1,
        subtype: $('#subtype').val() ? parseInt($('#subtype').val()) : 0,
        rtsp_url: $('#rtspUrl').val(),
        latitude: $('#latitude').val() ? parseFloat($('#latitude').val()) : null,
        longitude: $('#longitude').val() ? parseFloat($('#longitude').val()) : null,
        location_name: $('#locationName').val()
    };
    
    $.ajax({
        url: '/api/cameras',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#addCameraModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('Error adding camera: ' + xhr.responseJSON.error);
        }
    });
}
{% endif %}
</script>
{% endblock %}
