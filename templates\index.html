{% extends "base.html" %}

{% block title %}Dashboard - Camera Monitoring System{% endblock %}

{% block content %}
<div class="row">
    <!-- Camera Grid -->
    <div class="col-lg-8">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-video"></i> Live Cameras</h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshAllCameras()">
                    <i class="fas fa-sync-alt"></i> Refresh All
                </button>
                {% if current_user.can_manage_cameras() %}
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                    <i class="fas fa-plus"></i> Add Camera
                </button>
                {% endif %}
            </div>
        </div>

        <div class="row" id="cameraGrid">
            {% for camera in cameras %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card camera-card" data-camera-id="{{ camera.id }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ camera.name }}</h6>
                        <span class="badge bg-{{ 'success' if camera.status == 'online' else 'secondary' }}">
                            {{ camera.status }}
                        </span>
                    </div>
                    <div class="card-body p-0">
                        <div class="camera-stream-container">
                            <img src="{{ url_for('video_feed', camera_id=camera.id) }}" 
                                 class="camera-stream" 
                                 alt="Camera {{ camera.name }}"
                                 onerror="this.src='/static/images/camera-offline.png'">
                            <div class="camera-overlay">
                                <div class="camera-controls">
                                    <button class="btn btn-sm btn-light" onclick="viewCamera({{ camera.id }})">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    {% if current_user.can_manage_cameras() %}
                                    <button class="btn btn-sm btn-warning" onclick="editCamera({{ camera.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt"></i> 
                            {{ camera.location_name or 'No location set' }}
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
            
            {% if not cameras %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No cameras configured</h4>
                    <p class="text-muted">Add your first camera to start monitoring</p>
                    {% if current_user.can_manage_cameras() %}
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                        <i class="fas fa-plus"></i> Add Camera
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Map and Info Panel -->
    <div class="col-lg-4">
        <!-- Camera Map -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-map"></i> Camera Locations</h6>
            </div>
            <div class="card-body p-0">
                <div id="cameraMap" style="height: 300px;"></div>
            </div>
        </div>

        <!-- System Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> System Status</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-success" id="onlineCameras">0</h4>
                            <small class="text-muted">Online</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-warning" id="offlineCameras">0</h4>
                            <small class="text-muted">Offline</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <h4 class="text-primary" id="totalCameras">{{ cameras|length }}</h4>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-clock"></i> Recent Activity</h6>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <p class="text-muted">No recent activity</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Camera Modal -->
{% if current_user.can_manage_cameras() %}
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add New Camera</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">Camera Name *</label>
                                <input type="text" class="form-control" id="cameraName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">Camera Type *</label>
                                <select class="form-select" id="cameraType" required>
                                    <option value="">Select type...</option>
                                    <option value="dahua">Dahua</option>
                                    <option value="hikvision">Hikvision</option>
                                    <option value="ip_camera">Generic IP Camera</option>
                                    <option value="usb_camera">USB Camera</option>
                                    <option value="rtsp_stream">RTSP Stream</option>
                                    <option value="onvif">ONVIF Camera</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ipAddress" class="form-label">IP Address</label>
                                <input type="text" class="form-control" id="ipAddress" placeholder="*************">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="port" class="form-label">Port</label>
                                <input type="number" class="form-control" id="port" placeholder="554">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rtspUrl" class="form-label">RTSP URL (optional)</label>
                        <input type="text" class="form-control" id="rtspUrl" placeholder="rtsp://username:password@ip:port/stream">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" step="any" class="form-control" id="latitude" placeholder="40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" step="any" class="form-control" id="longitude" placeholder="-74.0060">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="locationName" class="form-label">Location Name</label>
                        <input type="text" class="form-control" id="locationName" placeholder="Front Door, Parking Lot, etc.">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">Add Camera</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize camera map
let cameraMap;
let cameraMarkers = [];

$(document).ready(function() {
    initializeMap();
    updateCameraStats();
    
    // Refresh camera stats every 30 seconds
    setInterval(updateCameraStats, 30000);
});

function initializeMap() {
    cameraMap = L.map('cameraMap').setView([40.7128, -74.0060], 10);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(cameraMap);
    
    // Add camera markers
    {% for camera in cameras %}
    {% if camera.latitude and camera.longitude %}
    addCameraMarker({{ camera.id }}, {{ camera.latitude }}, {{ camera.longitude }}, '{{ camera.name }}', '{{ camera.status }}');
    {% endif %}
    {% endfor %}
}

function addCameraMarker(cameraId, lat, lng, name, status) {
    const icon = L.divIcon({
        className: 'camera-marker',
        html: `<i class="fas fa-video" style="color: ${status === 'online' ? '#28a745' : '#6c757d'}"></i>`,
        iconSize: [20, 20]
    });
    
    const marker = L.marker([lat, lng], {icon: icon})
        .addTo(cameraMap)
        .bindPopup(`<strong>${name}</strong><br>Status: ${status}`)
        .on('click', function() {
            viewCamera(cameraId);
        });
    
    cameraMarkers.push(marker);
}

function updateCameraStats() {
    $.get('/api/cameras', function(cameras) {
        let online = 0, offline = 0;
        
        cameras.forEach(camera => {
            if (camera.status === 'online') online++;
            else offline++;
        });
        
        $('#onlineCameras').text(online);
        $('#offlineCameras').text(offline);
        $('#totalCameras').text(cameras.length);
    });
}

function viewCamera(cameraId) {
    window.location.href = `/camera/${cameraId}`;
}

function refreshAllCameras() {
    location.reload();
}

{% if current_user.can_manage_cameras() %}
function addCamera() {
    const formData = {
        name: $('#cameraName').val(),
        camera_type: $('#cameraType').val(),
        ip_address: $('#ipAddress').val(),
        port: $('#port').val() ? parseInt($('#port').val()) : null,
        username: $('#username').val(),
        password: $('#password').val(),
        rtsp_url: $('#rtspUrl').val(),
        latitude: $('#latitude').val() ? parseFloat($('#latitude').val()) : null,
        longitude: $('#longitude').val() ? parseFloat($('#longitude').val()) : null,
        location_name: $('#locationName').val()
    };
    
    $.ajax({
        url: '/api/cameras',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#addCameraModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('Error adding camera: ' + xhr.responseJSON.error);
        }
    });
}
{% endif %}
</script>
{% endblock %}
