#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الكاميرات - سكريبت التشغيل السريع
Camera Monitoring System - Quick Start Script
"""

import os
import sys
import subprocess
import webbrowser
import time
from config import Config

def print_arabic_banner():
    """طباعة شعار النظام بالعربية"""
    print("=" * 80)
    print("🎥 نظام مراقبة الكاميرات متعدد اللغات")
    print("📹 Multi-Language Camera Monitoring System")
    print("=" * 80)
    print("🌐 دعم كامل للغة العربية مع تخطيط RTL")
    print("🔧 Full Arabic support with RTL layout")
    print("=" * 80)

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print("❌ Error: Python 3.7 or newer is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} - متوافق")

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 فحص وتثبيت المتطلبات...")
    print("📦 Checking and installing requirements...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        print("✅ All requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print("⚠️ تحذير: بعض الحزم قد لا تكون مثبتة بشكل صحيح")
        print("⚠️ Warning: Some packages may not be installed correctly")
        print("💡 يمكنك المتابعة، ولكن قد تواجه مشاكل")
        print("💡 You can continue, but you may encounter issues")

def setup_demo_data():
    """إعداد البيانات التجريبية"""
    print("\n🎯 هل تريد إضافة كاميرات تجريبية؟")
    print("🎯 Do you want to add demo cameras?")
    
    choice = input("اكتب 'نعم' أو 'y' للموافقة (Type 'نعم' or 'y' to agree): ").lower()
    
    if choice in ['نعم', 'y', 'yes', 'ن']:
        try:
            subprocess.run([sys.executable, 'demo_setup.py'], input='y\n', 
                          text=True, check=True)
            print("✅ تم إعداد البيانات التجريبية بنجاح")
            print("✅ Demo data setup completed successfully")
        except subprocess.CalledProcessError:
            print("⚠️ فشل في إعداد البيانات التجريبية")
            print("⚠️ Failed to setup demo data")

def start_system():
    """بدء تشغيل النظام"""
    print("\n🚀 بدء تشغيل نظام مراقبة الكاميرات...")
    print("🚀 Starting Camera Monitoring System...")
    
    print(f"\n🌐 سيتم تشغيل النظام على: http://localhost:{Config.PORT}")
    print(f"🌐 System will run on: http://localhost:{Config.PORT}")
    
    print("\n🔑 بيانات الدخول الافتراضية:")
    print("🔑 Default login credentials:")
    print("   👤 اسم المستخدم / Username: admin")
    print("   🔒 كلمة المرور / Password: admin123")
    
    print("\n🌍 اللغات المدعومة:")
    print("🌍 Supported languages:")
    print("   🇸🇦 العربية (Arabic) - دعم كامل مع RTL")
    print("   🇺🇸 English - Full support")
    
    print("\n💡 نصائح:")
    print("💡 Tips:")
    print("   • استخدم Ctrl+C لإيقاف الخادم")
    print("   • Use Ctrl+C to stop the server")
    print("   • غيّر اللغة من القائمة العلوية")
    print("   • Change language from the top menu")
    print("   • أضف كاميراتك الحقيقية من لوحة الإدارة")
    print("   • Add your real cameras from admin panel")
    
    print("\n" + "=" * 80)
    
    # انتظار قليل ثم فتح المتصفح
    print("⏳ انتظار بدء الخادم...")
    print("⏳ Waiting for server to start...")
    
    try:
        # بدء تشغيل التطبيق
        from app import app
        
        # فتح المتصفح بعد ثانيتين
        def open_browser():
            time.sleep(2)
            try:
                # فتح بالعربية افتراضياً
                webbrowser.open(f'http://localhost:{Config.PORT}?language=ar')
            except:
                pass
        
        import threading
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("🎉 تم بدء النظام بنجاح!")
        print("🎉 System started successfully!")
        print("🌐 فتح المتصفح تلقائياً...")
        print("🌐 Opening browser automatically...")
        
        app.run(host=Config.HOST, port=Config.PORT, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
        print("👋 System stopped by user")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        print(f"❌ Error starting system: {str(e)}")
        sys.exit(1)

def main():
    """الوظيفة الرئيسية"""
    print_arabic_banner()
    
    print("\n🔍 فحص متطلبات النظام...")
    print("🔍 Checking system requirements...")
    
    # فحص إصدار Python
    check_python_version()
    
    # تثبيت المتطلبات
    install_requirements()
    
    # إعداد البيانات التجريبية
    if not os.path.exists(Config.DATABASE_PATH):
        setup_demo_data()
    
    # بدء النظام
    start_system()

if __name__ == "__main__":
    main()
