#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل سريع لمشكلة بطء التطبيق
Quick fix for application slowness
"""

import os
import sqlite3
import time

def quick_fix_slowness():
    """حل سريع لمشكلة البطء"""
    print("🚀 تطبيق حل سريع لمشكلة البطء...")
    print("🚀 Applying quick fix for slowness...")
    
    fixes_applied = 0
    
    try:
        # 1. تحسين قاعدة البيانات
        print("\n1️⃣ تحسين قاعدة البيانات...")
        conn = sqlite3.connect('camera_system.db')
        cursor = conn.cursor()
        
        # تحسين إعدادات SQLite
        optimizations = [
            "PRAGMA journal_mode=WAL",
            "PRAGMA synchronous=NORMAL", 
            "PRAGMA cache_size=10000",
            "PRAGMA temp_store=MEMORY",
            "PRAGMA mmap_size=268435456"  # 256MB
        ]
        
        for opt in optimizations:
            cursor.execute(opt)
            print(f"   ✅ {opt}")
        
        conn.commit()
        conn.close()
        fixes_applied += 1
        
    except Exception as e:
        print(f"   ❌ خطأ في تحسين قاعدة البيانات: {str(e)}")
    
    try:
        # 2. إنشاء ملف تكوين سريع
        print("\n2️⃣ إنشاء تكوين سريع...")
        
        fast_config = '''import os
from datetime import timedelta

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # Database configuration
    DATABASE_PATH = 'camera_system.db'
    
    # Server configuration - سريع
    HOST = '0.0.0.0'
    PORT = 4040
    DEBUG = False
    THREADED = True
    
    # Camera configuration - سريع
    CAMERA_TIMEOUT = 10
    STREAM_QUALITY = 'low'  # جودة منخفضة للسرعة
    MAX_CAMERAS = 10
    FRAME_RATE = 10
    BUFFER_SIZE = 1
    
    # Recording configuration
    RECORDING_PATH = 'recordings'
    MAX_RECORDING_DAYS = 3
    
    # Motion detection - مبسط
    MOTION_THRESHOLD = 50
    MOTION_MIN_AREA = 2000
    MOTION_DETECTION_INTERVAL = 5  # كل 5 ثوان
    
    # Performance settings - سريع
    DATABASE_POOL_SIZE = 5
    MAX_CONCURRENT_STREAMS = 3
    CACHE_TIMEOUT = 600
    ENABLE_COMPRESSION = True
    
    # Map configuration
    DEFAULT_MAP_CENTER = [40.7128, -74.0060]
    DEFAULT_ZOOM = 10
    
    # User session
    PERMANENT_SESSION_LIFETIME = timedelta(hours=6)
    
    # Logging - أقل
    LOG_LEVEL = 'ERROR'
    MAX_LOG_SIZE = 512 * 1024  # 512KB
'''
        
        with open('config_fast.py', 'w', encoding='utf-8') as f:
            f.write(fast_config)
        
        print("   ✅ تم إنشاء config_fast.py")
        fixes_applied += 1
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء التكوين: {str(e)}")
    
    try:
        # 3. تنظيف الملفات المؤقتة
        print("\n3️⃣ تنظيف الملفات المؤقتة...")
        
        temp_dirs = ['__pycache__', '.pytest_cache', 'recordings', 'snapshots']
        cleaned = 0
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    import shutil
                    if temp_dir in ['recordings', 'snapshots']:
                        # حذف الملفات القديمة فقط
                        for file in os.listdir(temp_dir):
                            file_path = os.path.join(temp_dir, file)
                            if os.path.isfile(file_path):
                                file_age = time.time() - os.path.getmtime(file_path)
                                if file_age > 86400:  # أكثر من يوم
                                    os.remove(file_path)
                                    cleaned += 1
                    else:
                        shutil.rmtree(temp_dir)
                        cleaned += 1
                except Exception:
                    pass
        
        print(f"   ✅ تم تنظيف {cleaned} ملف/مجلد")
        fixes_applied += 1
        
    except Exception as e:
        print(f"   ❌ خطأ في التنظيف: {str(e)}")
    
    try:
        # 4. إنشاء سكريبت تشغيل سريع
        print("\n4️⃣ إنشاء سكريبت تشغيل سريع...")
        
        fast_start = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للنظام
Fast system startup
"""

import os
import sys

# استخدام التكوين السريع
sys.path.insert(0, '.')
os.environ['FLASK_ENV'] = 'production'

# استيراد التكوين السريع
try:
    from config_fast import Config
    import config
    # استبدال التكوين
    for attr in dir(Config):
        if not attr.startswith('_'):
            setattr(config.Config, attr, getattr(Config, attr))
    print("✅ تم تحميل التكوين السريع")
except ImportError:
    print("⚠️ استخدام التكوين الافتراضي")

# تشغيل التطبيق
if __name__ == '__main__':
    from app import app
    from config import Config
    
    print("🚀 تشغيل النظام في الوضع السريع...")
    print(f"🌐 الرابط: http://{Config.HOST}:{Config.PORT}")
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=False,
        threaded=True,
        processes=1,
        use_reloader=False
    )
'''
        
        with open('app_fast.py', 'w', encoding='utf-8') as f:
            f.write(fast_start)
        
        print("   ✅ تم إنشاء app_fast.py")
        fixes_applied += 1
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء السكريبت: {str(e)}")
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الحل السريع")
    print("📊 Quick Fix Results")
    print("=" * 60)
    print(f"✅ تم تطبيق: {fixes_applied}/4 إصلاح")
    print(f"✅ Applied: {fixes_applied}/4 fixes")
    
    if fixes_applied >= 3:
        print("\n🎉 تم تطبيق معظم الإصلاحات!")
        print("🎉 Most fixes applied successfully!")
        
        print("\n🚀 لتشغيل النظام بسرعة:")
        print("🚀 To run the system fast:")
        print("   python app_fast.py")
        
        print("\n💡 نصائح إضافية:")
        print("💡 Additional tips:")
        print("   1. أغلق البرامج الأخرى")
        print("   2. استخدم جودة منخفضة للكاميرات")
        print("   3. قلل عدد الكاميرات النشطة")
        print("   4. أعد تشغيل الكمبيوتر إذا لزم الأمر")
        
    else:
        print("\n⚠️ لم يتم تطبيق جميع الإصلاحات")
        print("⚠️ Not all fixes were applied")
        print("💡 جرب إعادة تشغيل النظام")
        print("💡 Try restarting the system")

def test_speed():
    """اختبار سرعة النظام"""
    print("\n🧪 اختبار سرعة النظام...")
    print("🧪 Testing system speed...")
    
    try:
        import requests
        
        # اختبار سرعة الاستجابة
        start_time = time.time()
        response = requests.get('http://localhost:4040', timeout=10)
        response_time = time.time() - start_time
        
        print(f"   ⏱️ وقت الاستجابة: {response_time:.3f} ثانية")
        
        if response_time < 0.5:
            print("   🎉 سريع جداً!")
        elif response_time < 1.0:
            print("   ✅ سريع")
        elif response_time < 2.0:
            print("   ⚠️ متوسط")
        else:
            print("   ❌ بطيء")
            print("   💡 جرب: python app_fast.py")
        
        return response_time
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {str(e)}")
        print("   💡 تأكد من تشغيل النظام")
        return None

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🚀 حل سريع لمشكلة بطء التطبيق")
    print("🚀 Quick Fix for Application Slowness")
    print("=" * 60)
    
    # تطبيق الحل السريع
    quick_fix_slowness()
    
    # اختبار السرعة
    speed = test_speed()
    
    if speed and speed > 2.0:
        print(f"\n🔧 النظام ما زال بطيء ({speed:.3f}s)")
        print("🔧 System still slow")
        print("\n💡 حلول إضافية:")
        print("💡 Additional solutions:")
        print("   1. أعد تشغيل الكمبيوتر")
        print("   2. أغلق البرامج الأخرى")
        print("   3. استخدم: python app_fast.py")
        print("   4. قلل عدد الكاميرات")

if __name__ == "__main__":
    main()
