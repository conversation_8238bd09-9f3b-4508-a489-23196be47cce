{% extends "base.html" %}

{% block title %}{{ g.get_text('admin_panel') }} - {{ g.get_text('camera_monitoring_system') }}{% endblock %}

{% block extra_head %}
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-cog"></i> {{ g.get_text('admin_panel') }}</h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                <i class="fas fa-plus"></i> {{ g.get_text('add_camera') }}
            </button>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ cameras|length }}</h3>
                <p class="card-text">Total Cameras</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success" id="onlineCount">0</h3>
                <p class="card-text">Online</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning" id="offlineCount">0</h3>
                <p class="card-text">Offline</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-danger" id="errorCount">0</h3>
                <p class="card-text">Errors</p>
            </div>
        </div>
    </div>
</div>

<!-- Camera Management Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Camera Management</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="camerasTable">
                        <thead>
                            <tr>
                                <th>{{ g.get_text('id') }}</th>
                                <th>{{ g.get_text('name') }}</th>
                                <th>{{ g.get_text('type') }}</th>
                                <th>{{ g.get_text('ip_address') }}</th>
                                <th>{{ g.get_text('channel') }}</th>
                                <th>{{ g.get_text('status') }}</th>
                                <th>{{ g.get_text('location') }}</th>
                                <th>{{ g.get_text('last_seen') }}</th>
                                <th>{{ g.get_text('actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for camera in cameras %}
                            <tr data-camera-id="{{ camera.id }}">
                                <td>{{ camera.id }}</td>
                                <td>
                                    <strong>{{ camera.name }}</strong>
                                    {% if not camera.is_active %}
                                    <span class="badge bg-secondary ms-1">{{ g.get_text('inactive') }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ camera.camera_type.replace('_', ' ').title() }}</td>
                                <td>{{ camera.ip_address or 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ camera.channel or 1 }}</span>
                                    {% if camera.subtype is defined %}
                                    <small class="text-muted">/{{ camera.subtype }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if camera.status == 'online' else 'warning' if camera.status == 'offline' else 'danger' }}">
                                        {% if camera.status == 'online' %}{{ g.get_text('online') }}
                                        {% elif camera.status == 'offline' %}{{ g.get_text('offline') }}
                                        {% else %}{{ camera.status }}{% endif %}
                                    </span>
                                </td>
                                <td>{{ camera.location_name or g.get_text('not_set') }}</td>
                                <td>{{ camera.last_seen or g.get_text('never') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <!-- View Camera -->
                                        <button class="btn btn-outline-primary" onclick="viewCamera({{ camera.id }})" title="{{ g.get_text('view_camera') }}">
                                            <i class="fas fa-eye"></i>
                                        </button>

                                        <!-- Test Connection -->
                                        <button class="btn btn-outline-info" onclick="testCamera({{ camera.id }})" title="{{ g.get_text('test_connection') }}">
                                            <i class="fas fa-wifi"></i>
                                        </button>

                                        <!-- Edit Camera -->
                                        <button class="btn btn-outline-warning" onclick="editCamera({{ camera.id }})" title="{{ g.get_text('edit_camera') }}">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <!-- Dropdown for more actions -->
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="{{ g.get_text('more_actions') }}">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <!-- Start/Stop -->
                                                {% if camera.status == 'online' %}
                                                <li><a class="dropdown-item" href="#" onclick="stopCamera({{ camera.id }})">
                                                    <i class="fas fa-stop text-warning"></i> {{ g.get_text('stop_camera') }}
                                                </a></li>
                                                {% else %}
                                                <li><a class="dropdown-item" href="#" onclick="startCamera({{ camera.id }})">
                                                    <i class="fas fa-play text-success"></i> {{ g.get_text('start_camera') }}
                                                </a></li>
                                                {% endif %}

                                                <!-- Restart -->
                                                <li><a class="dropdown-item" href="#" onclick="restartCamera({{ camera.id }})">
                                                    <i class="fas fa-redo text-info"></i> {{ g.get_text('restart_camera') }}
                                                </a></li>

                                                <li><hr class="dropdown-divider"></li>

                                                <!-- Record -->
                                                <li><a class="dropdown-item" href="#" onclick="recordCamera({{ camera.id }})">
                                                    <i class="fas fa-video text-danger"></i> {{ g.get_text('start_recording') }}
                                                </a></li>

                                                <!-- Snapshot -->
                                                <li><a class="dropdown-item" href="#" onclick="takeSnapshot({{ camera.id }})">
                                                    <i class="fas fa-camera text-primary"></i> {{ g.get_text('take_snapshot') }}
                                                </a></li>

                                                <li><hr class="dropdown-divider"></li>

                                                <!-- Toggle Active/Inactive -->
                                                {% if camera.is_active %}
                                                <li><a class="dropdown-item" href="#" onclick="toggleCamera({{ camera.id }}, false)">
                                                    <i class="fas fa-pause text-secondary"></i> {{ g.get_text('deactivate_camera') }}
                                                </a></li>
                                                {% else %}
                                                <li><a class="dropdown-item" href="#" onclick="toggleCamera({{ camera.id }}, true)">
                                                    <i class="fas fa-play text-success"></i> {{ g.get_text('activate_camera') }}
                                                </a></li>
                                                {% endif %}

                                                <li><hr class="dropdown-divider"></li>

                                                <!-- Delete -->
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCamera({{ camera.id }})">
                                                    <i class="fas fa-trash"></i> {{ g.get_text('delete_camera') }}
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Logs -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-alt"></i> System Logs</h5>
            </div>
            <div class="card-body">
                <div id="systemLogs" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                    <p class="text-muted">Loading system logs...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Camera Modal -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add New Camera</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">Camera Name *</label>
                                <input type="text" class="form-control" id="cameraName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">Camera Type *</label>
                                <select class="form-select" id="cameraType" required>
                                    <option value="">Select type...</option>
                                    <option value="dahua">Dahua</option>
                                    <option value="hikvision">Hikvision</option>
                                    <option value="ip_camera">Generic IP Camera</option>
                                    <option value="usb_camera">USB Camera</option>
                                    <option value="rtsp_stream">RTSP Stream</option>
                                    <option value="onvif">ONVIF Camera</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ipAddress" class="form-label">IP Address</label>
                                <input type="text" class="form-control" id="ipAddress" placeholder="*************">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="port" class="form-label">Port</label>
                                <input type="number" class="form-control" id="port" placeholder="554">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">{{ g.get_text('username') }}</label>
                                <input type="text" class="form-control" id="username">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">{{ g.get_text('password') }}</label>
                                <input type="password" class="form-control" id="password">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel" class="form-label">{{ g.get_text('channel') }}</label>
                                <input type="number" class="form-control" id="channel" placeholder="1" value="1" min="1" max="16">
                                <small class="form-text text-muted">{{ g.get_text('channel_help') }}</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subtype" class="form-label">{{ g.get_text('subtype') }}</label>
                                <select class="form-select" id="subtype">
                                    <option value="0">0 - {{ g.get_text('main_stream') }}</option>
                                    <option value="1">1 - {{ g.get_text('sub_stream') }}</option>
                                </select>
                                <small class="form-text text-muted">{{ g.get_text('subtype_help') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rtspUrl" class="form-label">RTSP URL (optional)</label>
                        <input type="text" class="form-control" id="rtspUrl" placeholder="rtsp://username:password@ip:port/stream">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" step="any" class="form-control" id="latitude" placeholder="40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" step="any" class="form-control" id="longitude" placeholder="-74.0060">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="locationName" class="form-label">Location Name</label>
                        <input type="text" class="form-control" id="locationName" placeholder="Front Door, Parking Lot, etc.">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">Add Camera</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Camera Modal -->
<div class="modal fade" id="editCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> {{ g.get_text('edit_camera') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCameraForm">
                    <input type="hidden" id="editCameraId">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editCameraName" class="form-label">{{ g.get_text('camera_name') }} *</label>
                                <input type="text" class="form-control" id="editCameraName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editCameraType" class="form-label">{{ g.get_text('camera_type') }} *</label>
                                <select class="form-select" id="editCameraType" required>
                                    <option value="">{{ g.get_text('select_type') }}</option>
                                    <option value="dahua">Dahua</option>
                                    <option value="hikvision">Hikvision</option>
                                    <option value="ip_camera">{{ g.get_text('generic_ip_camera') }}</option>
                                    <option value="usb_camera">{{ g.get_text('usb_camera') }}</option>
                                    <option value="rtsp_stream">{{ g.get_text('rtsp_stream') }}</option>
                                    <option value="onvif">{{ g.get_text('onvif_camera') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editIpAddress" class="form-label">{{ g.get_text('ip_address') }}</label>
                                <input type="text" class="form-control" id="editIpAddress" placeholder="*************">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPort" class="form-label">{{ g.get_text('port') }}</label>
                                <input type="number" class="form-control" id="editPort" placeholder="554">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUsername" class="form-label">{{ g.get_text('username') }}</label>
                                <input type="text" class="form-control" id="editUsername">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPassword" class="form-label">{{ g.get_text('password') }}</label>
                                <input type="password" class="form-control" id="editPassword">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editChannel" class="form-label">{{ g.get_text('channel') }}</label>
                                <input type="number" class="form-control" id="editChannel" placeholder="1" value="1" min="1" max="16">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editSubtype" class="form-label">{{ g.get_text('subtype') }}</label>
                                <select class="form-select" id="editSubtype">
                                    <option value="0">0 - {{ g.get_text('main_stream') }}</option>
                                    <option value="1">1 - {{ g.get_text('sub_stream') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editLatitude" class="form-label">{{ g.get_text('latitude') }}</label>
                                <input type="number" step="any" class="form-control" id="editLatitude" placeholder="40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editLongitude" class="form-label">{{ g.get_text('longitude') }}</label>
                                <input type="number" step="any" class="form-control" id="editLongitude" placeholder="-74.0060">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editLocationName" class="form-label">{{ g.get_text('location_name') }}</label>
                        <input type="text" class="form-control" id="editLocationName" placeholder="{{ g.get_text('location_placeholder') }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ g.get_text('cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="updateCamera()">{{ g.get_text('save_changes') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    updateCameraStats();
    loadSystemLogs();
    
    // Refresh stats every 30 seconds
    setInterval(updateCameraStats, 30000);
    setInterval(loadSystemLogs, 60000);
});

function updateCameraStats() {
    $.get('/api/cameras', function(cameras) {
        let online = 0, offline = 0, error = 0;
        
        cameras.forEach(camera => {
            if (camera.status === 'online') online++;
            else if (camera.status === 'error') error++;
            else offline++;
        });
        
        $('#onlineCount').text(online);
        $('#offlineCount').text(offline);
        $('#errorCount').text(error);
    });
}

function loadSystemLogs() {
    // Simulate system logs - in real implementation, this would fetch from API
    const logs = [
        { time: new Date().toLocaleString(), level: 'INFO', message: 'System started successfully' },
        { time: new Date(Date.now() - 300000).toLocaleString(), level: 'WARNING', message: 'Camera 3 connection timeout' },
        { time: new Date(Date.now() - 600000).toLocaleString(), level: 'INFO', message: 'Motion detected on Camera 1' },
        { time: new Date(Date.now() - 900000).toLocaleString(), level: 'ERROR', message: 'Failed to connect to Camera 5' }
    ];
    
    let logHtml = '';
    logs.forEach(log => {
        const levelClass = log.level === 'ERROR' ? 'text-danger' : 
                          log.level === 'WARNING' ? 'text-warning' : 'text-info';
        logHtml += `<div class="mb-2">
            <span class="text-muted">[${log.time}]</span>
            <span class="${levelClass}">[${log.level}]</span>
            ${log.message}
        </div>`;
    });
    
    $('#systemLogs').html(logHtml);
}

function viewCamera(cameraId) {
    window.location.href = `/camera/${cameraId}`;
}

function testCamera(cameraId) {
    // Show loading state
    const button = $(`button[onclick="testCamera(${cameraId})"]`);
    const originalHtml = button.html();
    button.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);

    $.post(`/api/cameras/${cameraId}/test`, function(response) {
        const message = response.working ?
            '{{ g.get_text("camera_working") if g.get_text("camera_working") != "camera_working" else "الكاميرا تعمل بشكل جيد!" }}' :
            '{{ g.get_text("camera_connection_failed") if g.get_text("camera_connection_failed") != "camera_connection_failed" else "فشل في الاتصال بالكاميرا!" }}';

        alert(message);
    }).fail(function() {
        alert('{{ g.get_text("error_testing_camera") if g.get_text("error_testing_camera") != "error_testing_camera" else "خطأ في اختبار الكاميرا" }}');
    }).always(function() {
        // Restore button state
        button.html(originalHtml).prop('disabled', false);
    });
}

function startCamera(cameraId) {
    $.post(`/api/cameras/${cameraId}/start`, function(response) {
        if (response.success) {
            alert('Camera started successfully');
            updateCameraStats();
        } else {
            alert('Failed to start camera');
        }
    });
}

function stopCamera(cameraId) {
    $.post(`/api/cameras/${cameraId}/stop`, function(response) {
        if (response.success) {
            alert('Camera stopped successfully');
            updateCameraStats();
        } else {
            alert('Failed to stop camera');
        }
    });
}

function editCamera(cameraId) {
    // Get camera data from the table row
    const row = $(`tr[data-camera-id="${cameraId}"]`);
    const cells = row.find('td');

    // Extract camera data
    const cameraData = {
        id: cameraId,
        name: cells.eq(1).find('strong').text(),
        type: cells.eq(2).text().toLowerCase().replace(' ', '_'),
        ip: cells.eq(3).text(),
        location: cells.eq(6).text()
    };

    // Fill edit form
    $('#editCameraId').val(cameraData.id);
    $('#editCameraName').val(cameraData.name);
    $('#editCameraType').val(cameraData.type);
    $('#editIpAddress').val(cameraData.ip === 'N/A' ? '' : cameraData.ip);
    $('#editLocationName').val(cameraData.location === '{{ g.get_text("not_set") }}' ? '' : cameraData.location);

    // Show edit modal
    $('#editCameraModal').modal('show');
}

function updateCamera() {
    const cameraId = $('#editCameraId').val();
    const formData = {
        name: $('#editCameraName').val(),
        camera_type: $('#editCameraType').val(),
        ip_address: $('#editIpAddress').val(),
        port: $('#editPort').val() ? parseInt($('#editPort').val()) : null,
        username: $('#editUsername').val(),
        password: $('#editPassword').val(),
        channel: $('#editChannel').val() ? parseInt($('#editChannel').val()) : 1,
        subtype: $('#editSubtype').val() ? parseInt($('#editSubtype').val()) : 0,
        latitude: $('#editLatitude').val() ? parseFloat($('#editLatitude').val()) : null,
        longitude: $('#editLongitude').val() ? parseFloat($('#editLongitude').val()) : null,
        location_name: $('#editLocationName').val()
    };

    $.ajax({
        url: `/api/cameras/${cameraId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#editCameraModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('{{ g.get_text("error") }}: ' + xhr.responseJSON.error);
        }
    });
}

function deleteCamera(cameraId) {
    if (confirm('{{ g.get_text("confirm_delete_camera") }}')) {
        $.ajax({
            url: `/api/cameras/${cameraId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    alert('{{ g.get_text("camera_deleted") }}');
                    location.reload();
                } else {
                    alert('{{ g.get_text("error") }}: ' + (response.error || 'Unknown error'));
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Network error';
                alert('{{ g.get_text("error") }}: ' + errorMsg);
            }
        });
    }
}

function toggleCamera(cameraId, activate) {
    $.ajax({
        url: `/api/cameras/${cameraId}/toggle`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ active: activate }),
        success: function(response) {
            location.reload();
        },
        error: function(xhr) {
            alert('{{ g.get_text("error") }}: ' + xhr.responseJSON.error);
        }
    });
}

function restartCamera(cameraId) {
    if (confirm('{{ g.get_text("confirm_restart_camera") }}')) {
        $.ajax({
            url: `/api/cameras/${cameraId}/restart`,
            method: 'POST',
            success: function(response) {
                alert('{{ g.get_text("camera_restarted") }}');
                updateCameraStats();
            },
            error: function(xhr) {
                alert('{{ g.get_text("error") }}: ' + xhr.responseJSON.error);
            }
        });
    }
}

function recordCamera(cameraId) {
    $.ajax({
        url: `/api/cameras/${cameraId}/record`,
        method: 'POST',
        success: function(response) {
            alert('{{ g.get_text("recording_started") }}');
        },
        error: function(xhr) {
            alert('{{ g.get_text("error") }}: ' + xhr.responseJSON.error);
        }
    });
}

function takeSnapshot(cameraId) {
    $.ajax({
        url: `/api/cameras/${cameraId}/snapshot`,
        method: 'POST',
        success: function(response) {
            alert('{{ g.get_text("snapshot_taken") }}');
        },
        error: function(xhr) {
            alert('{{ g.get_text("error") }}: ' + xhr.responseJSON.error);
        }
    });
}

function addCamera() {
    const formData = {
        name: $('#cameraName').val(),
        camera_type: $('#cameraType').val(),
        ip_address: $('#ipAddress').val(),
        port: $('#port').val() ? parseInt($('#port').val()) : null,
        username: $('#username').val(),
        password: $('#password').val(),
        channel: $('#channel').val() ? parseInt($('#channel').val()) : 1,
        subtype: $('#subtype').val() ? parseInt($('#subtype').val()) : 0,
        rtsp_url: $('#rtspUrl').val(),
        latitude: $('#latitude').val() ? parseFloat($('#latitude').val()) : null,
        longitude: $('#longitude').val() ? parseFloat($('#longitude').val()) : null,
        location_name: $('#locationName').val()
    };
    
    $.ajax({
        url: '/api/cameras',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#addCameraModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('Error adding camera: ' + xhr.responseJSON.error);
        }
    });
}
</script>
{% endblock %}
