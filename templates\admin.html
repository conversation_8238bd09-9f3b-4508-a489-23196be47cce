{% extends "base.html" %}

{% block title %}Admin Panel - Camera Monitoring System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-cog"></i> Admin Panel</h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                <i class="fas fa-plus"></i> Add Camera
            </button>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ cameras|length }}</h3>
                <p class="card-text">Total Cameras</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success" id="onlineCount">0</h3>
                <p class="card-text">Online</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning" id="offlineCount">0</h3>
                <p class="card-text">Offline</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-danger" id="errorCount">0</h3>
                <p class="card-text">Errors</p>
            </div>
        </div>
    </div>
</div>

<!-- Camera Management Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Camera Management</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="camerasTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>IP Address</th>
                                <th>Status</th>
                                <th>Location</th>
                                <th>Last Seen</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for camera in cameras %}
                            <tr data-camera-id="{{ camera.id }}">
                                <td>{{ camera.id }}</td>
                                <td>
                                    <strong>{{ camera.name }}</strong>
                                    {% if not camera.is_active %}
                                    <span class="badge bg-secondary ms-1">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ camera.camera_type.replace('_', ' ').title() }}</td>
                                <td>{{ camera.ip_address or 'N/A' }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if camera.status == 'online' else 'warning' if camera.status == 'offline' else 'danger' }}">
                                        {{ camera.status }}
                                    </span>
                                </td>
                                <td>{{ camera.location_name or 'Not set' }}</td>
                                <td>{{ camera.last_seen or 'Never' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewCamera({{ camera.id }})" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="testCamera({{ camera.id }})" title="Test">
                                            <i class="fas fa-wifi"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editCamera({{ camera.id }})" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="startCamera({{ camera.id }})" title="Start">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="stopCamera({{ camera.id }})" title="Stop">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteCamera({{ camera.id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Logs -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-alt"></i> System Logs</h5>
            </div>
            <div class="card-body">
                <div id="systemLogs" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                    <p class="text-muted">Loading system logs...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Camera Modal -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add New Camera</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraName" class="form-label">Camera Name *</label>
                                <input type="text" class="form-control" id="cameraName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cameraType" class="form-label">Camera Type *</label>
                                <select class="form-select" id="cameraType" required>
                                    <option value="">Select type...</option>
                                    <option value="dahua">Dahua</option>
                                    <option value="hikvision">Hikvision</option>
                                    <option value="ip_camera">Generic IP Camera</option>
                                    <option value="usb_camera">USB Camera</option>
                                    <option value="rtsp_stream">RTSP Stream</option>
                                    <option value="onvif">ONVIF Camera</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ipAddress" class="form-label">IP Address</label>
                                <input type="text" class="form-control" id="ipAddress" placeholder="*************">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="port" class="form-label">Port</label>
                                <input type="number" class="form-control" id="port" placeholder="554">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rtspUrl" class="form-label">RTSP URL (optional)</label>
                        <input type="text" class="form-control" id="rtspUrl" placeholder="rtsp://username:password@ip:port/stream">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" step="any" class="form-control" id="latitude" placeholder="40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" step="any" class="form-control" id="longitude" placeholder="-74.0060">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="locationName" class="form-label">Location Name</label>
                        <input type="text" class="form-control" id="locationName" placeholder="Front Door, Parking Lot, etc.">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addCamera()">Add Camera</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    updateCameraStats();
    loadSystemLogs();
    
    // Refresh stats every 30 seconds
    setInterval(updateCameraStats, 30000);
    setInterval(loadSystemLogs, 60000);
});

function updateCameraStats() {
    $.get('/api/cameras', function(cameras) {
        let online = 0, offline = 0, error = 0;
        
        cameras.forEach(camera => {
            if (camera.status === 'online') online++;
            else if (camera.status === 'error') error++;
            else offline++;
        });
        
        $('#onlineCount').text(online);
        $('#offlineCount').text(offline);
        $('#errorCount').text(error);
    });
}

function loadSystemLogs() {
    // Simulate system logs - in real implementation, this would fetch from API
    const logs = [
        { time: new Date().toLocaleString(), level: 'INFO', message: 'System started successfully' },
        { time: new Date(Date.now() - 300000).toLocaleString(), level: 'WARNING', message: 'Camera 3 connection timeout' },
        { time: new Date(Date.now() - 600000).toLocaleString(), level: 'INFO', message: 'Motion detected on Camera 1' },
        { time: new Date(Date.now() - 900000).toLocaleString(), level: 'ERROR', message: 'Failed to connect to Camera 5' }
    ];
    
    let logHtml = '';
    logs.forEach(log => {
        const levelClass = log.level === 'ERROR' ? 'text-danger' : 
                          log.level === 'WARNING' ? 'text-warning' : 'text-info';
        logHtml += `<div class="mb-2">
            <span class="text-muted">[${log.time}]</span>
            <span class="${levelClass}">[${log.level}]</span>
            ${log.message}
        </div>`;
    });
    
    $('#systemLogs').html(logHtml);
}

function viewCamera(cameraId) {
    window.location.href = `/camera/${cameraId}`;
}

function testCamera(cameraId) {
    $.post(`/api/cameras/${cameraId}/test`, function(response) {
        const icon = response.working ? 'check' : 'times';
        const color = response.working ? 'success' : 'danger';
        const message = response.working ? 'Camera is working!' : 'Camera connection failed!';
        
        alert(message);
    }).fail(function() {
        alert('Error testing camera connection');
    });
}

function startCamera(cameraId) {
    $.post(`/api/cameras/${cameraId}/start`, function(response) {
        if (response.success) {
            alert('Camera started successfully');
            updateCameraStats();
        } else {
            alert('Failed to start camera');
        }
    });
}

function stopCamera(cameraId) {
    $.post(`/api/cameras/${cameraId}/stop`, function(response) {
        if (response.success) {
            alert('Camera stopped successfully');
            updateCameraStats();
        } else {
            alert('Failed to stop camera');
        }
    });
}

function editCamera(cameraId) {
    // TODO: Implement camera editing
    alert('Camera editing functionality coming soon!');
}

function deleteCamera(cameraId) {
    if (confirm('Are you sure you want to delete this camera?')) {
        // TODO: Implement camera deletion
        alert('Camera deletion functionality coming soon!');
    }
}

function addCamera() {
    const formData = {
        name: $('#cameraName').val(),
        camera_type: $('#cameraType').val(),
        ip_address: $('#ipAddress').val(),
        port: $('#port').val() ? parseInt($('#port').val()) : null,
        username: $('#username').val(),
        password: $('#password').val(),
        rtsp_url: $('#rtspUrl').val(),
        latitude: $('#latitude').val() ? parseFloat($('#latitude').val()) : null,
        longitude: $('#longitude').val() ? parseFloat($('#longitude').val()) : null,
        location_name: $('#locationName').val()
    };
    
    $.ajax({
        url: '/api/cameras',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#addCameraModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('Error adding camera: ' + xhr.responseJSON.error);
        }
    });
}
</script>
{% endblock %}
