# 📷 نظام مراقبة الكاميرات متعدد اللغات

نظام مراقبة شامل قائم على الويب باستخدام Python Flask يدعم أنواع متعددة من الكاميرات مثل داهوا، هيكفيجن، كاميرات IP، كاميرات USB، وبث RTSP.

## 🌟 المميزات

### الوظائف الأساسية
- **دعم متعدد الكاميرات**: داهوا، هيكفيجن، كاميرا IP، كاميرا USB، بث RTSP، ONVIF
- **البث المباشر**: تدفق فيديو مباشر باستخدام OpenCV و Flask
- **خريطة تفاعلية**: عرض مواقع الكاميرات على خريطة تفاعلية
- **نظام المصادقة**: وصول قائم على الأدوار (مدير، مشرف، مشاهد)
- **كشف الحركة**: كشف تلقائي للحركة مع التنبيهات
- **التسجيل**: إمكانيات تسجيل الفيديو
- **تصميم متجاوب**: يعمل على سطح المكتب والهاتف المحمول
- **دعم متعدد اللغات**: العربية والإنجليزية

### المميزات المتقدمة
- **دعم بروتوكول ONVIF**: الاتصال بكاميرات متوافقة مع ONVIF
- **تنبيهات الحركة**: إشعارات عبر البريد الإلكتروني و WhatsApp
- **مراقبة النظام**: مراقبة الصحة والإحصائيات
- **تنظيف تلقائي**: تنظيف تلقائي للتسجيلات القديمة
- **التقاط اللقطات**: التقاط لقطات من البث المباشر
- **وضع ملء الشاشة**: عرض الكاميرا بملء الشاشة

## 🚀 البدء السريع

### 1. التثبيت

```bash
# استنساخ أو تحميل ملفات المشروع
cd camera-monitoring-system

# تثبيت الحزم المطلوبة
pip install -r requirements.txt
```

### 2. التكوين

قم بتحرير `config.py` لتخصيص إعداداتك:

```python
# تكوين الخادم
HOST = '0.0.0.0'
PORT = 4040

# إعدادات الكاميرا
STREAM_QUALITY = 'medium'  # منخفض/متوسط/عالي
MAX_CAMERAS = 50

# مركز الخريطة (موقعك)
DEFAULT_MAP_CENTER = [40.7128, -74.0060]  # إحداثيات نيويورك
```

### 3. تشغيل التطبيق

```bash
python app.py
```

زيارة: **http://localhost:4040**

### 4. بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📋 متطلبات النظام

- Python 3.7+
- OpenCV 4.x
- Flask 2.x
- SQLite (مضمن)
- متصفح ويب حديث

## 🎯 أنواع الكاميرات المدعومة

### 1. كاميرات داهوا
```
النوع: dahua
رابط RTSP: rtsp://username:password@ip:554/cam/realmonitor?channel=1&subtype=0
```

### 2. كاميرات هيكفيجن
```
النوع: hikvision
رابط RTSP: rtsp://username:password@ip:554/Streaming/Channels/101
```

### 3. كاميرات IP العامة
```
النوع: ip_camera
رابط RTSP: rtsp://username:password@ip:554/stream1
```

### 4. كاميرات USB
```
النوع: usb_camera
فهرس الجهاز: 0، 1، 2، إلخ.
```

### 5. بث RTSP
```
النوع: rtsp_stream
رابط RTSP مخصص: rtsp://your-stream-url
```

### 6. كاميرات ONVIF
```
النوع: onvif
عنوان IP: عنوان IP للكاميرا
المنفذ: 80 (افتراضي)
اسم المستخدم/كلمة المرور: بيانات اعتماد الكاميرا
```

## 🔧 دليل التكوين

### إضافة الكاميرات

1. **تسجيل الدخول** كمشرف أو مدير
2. **انقر على "إضافة كاميرا"**
3. **املأ تفاصيل الكاميرا**:
   - الاسم: اسم وصفي
   - النوع: اختر نوع الكاميرا
   - عنوان IP: عنوان IP للكاميرا
   - المنفذ: منفذ الكاميرا (عادة 554 لـ RTSP)
   - اسم المستخدم/كلمة المرور: بيانات اعتماد الكاميرا
   - الموقع: إحداثيات GPS ووصف

### تكوين أنواع الكاميرات

#### لكاميرات داهوا:
- عنوان IP: `*************`
- المنفذ: `554`
- اسم المستخدم: `admin`
- كلمة المرور: `your_password`

#### لكاميرات هيكفيجن:
- عنوان IP: `*************`
- المنفذ: `554`
- اسم المستخدم: `admin`
- كلمة المرور: `your_password`

#### لكاميرات USB:
- عنوان IP: `0` (فهرس الجهاز)
- اترك الحقول الأخرى فارغة

#### لـ RTSP المخصص:
- املأ رابط RTSP الكامل في حقل رابط RTSP

## 🗺️ تكامل الخريطة

يستخدم النظام OpenStreetMap (Leaflet) لعرض مواقع الكاميرات:

1. **أضف إحداثيات GPS** عند إنشاء الكاميرات
2. **انقر على علامات الكاميرا** على الخريطة لعرض البث المباشر
3. **خصص مركز الخريطة** في `config.py`

## 🔐 إدارة المستخدمين

### أدوار المستخدمين:
- **مشاهد (1)**: يمكنه فقط عرض تدفقات الكاميرا
- **مشرف (2)**: يمكنه إضافة/تحرير الكاميرات وعرض التدفقات
- **مدير (3)**: وصول كامل للنظام

### إنشاء المستخدمين:
```python
# في وحدة تحكم Python أو أضف إلى database.py
db_manager = DatabaseManager()
db_manager.create_user('username', 'password', role=2, email='<EMAIL>')
```

## 🌐 الدعم متعدد اللغات

### اللغات المدعومة:
- **العربية** (ar) - دعم كامل مع RTL
- **الإنجليزية** (en) - اللغة الافتراضية

### تغيير اللغة:
- استخدم القائمة المنسدلة في شريط التنقل
- أو أضف `?language=ar` إلى الرابط
- يتم حفظ تفضيل اللغة في الجلسة

### إضافة لغات جديدة:
1. أضف الترجمات في `translations.py`
2. أضف دعم CSS إذا لزم الأمر
3. أضف اللغة إلى قائمة اللغات في القوالب

## 📧 إعداد الإشعارات

### إشعارات البريد الإلكتروني:
قم بتحرير `config.py`:
```python
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your_app_password'
```

### إشعارات WhatsApp:
قم بتكوين مفتاح WhatsApp API في `config.py`:
```python
WHATSAPP_API_KEY = 'your_whatsapp_api_key'
```

## 🎮 دليل الاستخدام

### مميزات لوحة التحكم:
- **شبكة الكاميرات المباشرة**: عرض جميع الكاميرات في وقت واحد
- **حالة الكاميرا**: مؤشرات متصل/غير متصل
- **خريطة تفاعلية**: انقر على العلامات لعرض الكاميرات
- **إحصائيات النظام**: أعداد الكاميرات والحالة
- **إجراءات سريعة**: تحديث، إضافة كاميرات

### عرض الكاميرا الفردية:
- **وضع ملء الشاشة**: اضغط F11 أو انقر على زر ملء الشاشة
- **التقاط لقطة**: التقاط الإطار الحالي
- **بدء/إيقاف التسجيل**: تسجيل الفيديو
- **كشف الحركة**: تفعيل/إلغاء تفعيل تنبيهات الحركة

### لوحة الإدارة:
- **إدارة الكاميرات**: إضافة، تحرير، حذف الكاميرات
- **مراقبة النظام**: عرض صحة النظام
- **إدارة المستخدمين**: إدارة حسابات المستخدمين
- **سجلات النظام**: عرض نشاط النظام

## 🔧 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة:

#### الكاميرا لا تتصل:
1. تحقق من عنوان IP والمنفذ
2. تحقق من اسم المستخدم/كلمة المرور
3. اختبر رابط الكاميرا في مشغل VLC
4. تحقق من اتصال الشبكة
5. تحقق من أن الكاميرا تدعم RTSP

#### جودة فيديو ضعيفة:
1. اضبط `STREAM_QUALITY` في config.py
2. تحقق من عرض النطاق الترددي للشبكة
3. قلل عدد التدفقات المتزامنة

#### كشف الحركة لا يعمل:
1. تحقق من ظروف الإضاءة للكاميرا
2. اضبط `MOTION_THRESHOLD` في config.py
3. تحقق من تفعيل كشف الحركة

#### مشاكل الأداء:
1. قلل عدد الكاميرات النشطة
2. اخفض جودة التدفق
3. تحقق من موارد النظام (المعالج/الذاكرة)

## 📁 هيكل المشروع

```
camera-monitoring-system/
├── app.py                 # تطبيق Flask الرئيسي
├── config.py             # إعدادات التكوين
├── database.py           # إدارة قاعدة البيانات
├── camera_manager.py     # معالجة الكاميرات
├── auth.py              # نظام المصادقة
├── utils.py             # وظائف المساعدة
├── translations.py      # نظام الترجمة
├── demo_setup.py        # إعداد البيانات التجريبية
├── start_system.py      # سكريبت البدء
├── requirements.txt     # التبعيات
├── README.md           # التوثيق (إنجليزي)
├── README_AR.md        # التوثيق (عربي)
├── templates/          # قوالب HTML
├── static/            # ملفات CSS، JS، الصور
└── recordings/        # تسجيلات الفيديو (يتم إنشاؤها تلقائياً)
```

## 🔒 ملاحظات الأمان

1. **غيّر كلمة مرور المدير الافتراضية** فوراً
2. **استخدم HTTPS** في الإنتاج
3. **قم بتكوين جدار الحماية** بشكل صحيح
4. **تحديثات أمنية منتظمة**
5. **كلمات مرور قوية للكاميرات**

## 🚀 النشر في الإنتاج

### استخدام Gunicorn:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:4040 app:app
```

### استخدام Docker:
```dockerfile
FROM python:3.9
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 4040
CMD ["python", "app.py"]
```

## 📝 نقاط النهاية API

- `GET /api/cameras` - الحصول على جميع الكاميرات
- `POST /api/cameras` - إضافة كاميرا جديدة
- `POST /api/cameras/{id}/test` - اختبار اتصال الكاميرا
- `POST /api/cameras/{id}/start` - بدء تدفق الكاميرا
- `POST /api/cameras/{id}/stop` - إيقاف تدفق الكاميرا
- `GET /video_feed/{id}` - تدفق الفيديو المباشر
- `GET /set_language/{lang}` - تغيير اللغة

## 🤝 المساهمة

1. فرع المستودع
2. إنشاء فرع الميزة
3. إجراء التغييرات
4. اختبار شامل
5. إرسال طلب سحب

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح تحت ترخيص MIT.

## 🆘 الدعم

للدعم والأسئلة:
1. تحقق من قسم استكشاف الأخطاء وإصلاحها
2. راجع توثيق الكاميرا
3. اختبر مع مشغل VLC أولاً
4. تحقق من سجلات النظام في لوحة الإدارة

---

**مراقبة سعيدة! 📹🔍**

## 🌟 المميزات الجديدة - الدعم العربي

### ✅ تم إضافة:
- **واجهة عربية كاملة** مع دعم RTL
- **خط Cairo** للنصوص العربية
- **ترجمة شاملة** لجميع عناصر الواجهة
- **تبديل اللغة** في الوقت الفعلي
- **Bootstrap RTL** للتخطيط الصحيح
- **حفظ تفضيل اللغة** في الجلسة

### 🎯 كيفية الاستخدام:
1. **انقر على قائمة اللغة** في شريط التنقل
2. **اختر "العربية"** للتبديل للعربية
3. **اختر "English"** للعودة للإنجليزية
4. **أو استخدم الرابط**: `http://localhost:4040?language=ar`

النظام الآن يدعم اللغة العربية بالكامل مع تخطيط RTL صحيح! 🎉
