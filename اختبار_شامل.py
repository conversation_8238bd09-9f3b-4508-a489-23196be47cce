#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام مراقبة الكاميرات
Comprehensive test for camera monitoring system
"""

import requests
import time
import json
from database import DatabaseManager
from translations import get_text

def test_server():
    """اختبار الخادم"""
    print("🌐 اختبار الخادم...")
    print("🌐 Testing server...")
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get('http://localhost:4040', timeout=5)
        if response.status_code == 200:
            print("   ✅ الصفحة الرئيسية تعمل")
            print("   ✅ Main page working")
        else:
            print(f"   ❌ خطأ في الصفحة الرئيسية: {response.status_code}")
            return False
        
        # اختبار الصفحة العربية
        ar_response = requests.get('http://localhost:4040?language=ar', timeout=5)
        if 'مراقب الكاميرات' in ar_response.text:
            print("   ✅ الدعم العربي يعمل")
            print("   ✅ Arabic support working")
        else:
            print("   ⚠️ مشكلة في الدعم العربي")
            print("   ⚠️ Issue with Arabic support")
        
        # اختبار صفحة تسجيل الدخول
        login_response = requests.get('http://localhost:4040/login', timeout=5)
        if login_response.status_code == 200:
            print("   ✅ صفحة تسجيل الدخول تعمل")
            print("   ✅ Login page working")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالخادم")
        print("   ❌ Cannot connect to server")
        return False
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        print(f"   ❌ Error: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    print("🗄️ Testing database...")
    
    try:
        db = DatabaseManager()
        
        # اختبار الحصول على الكاميرات
        cameras = db.get_cameras()
        print(f"   📹 عدد الكاميرات: {len(cameras)}")
        print(f"   📹 Number of cameras: {len(cameras)}")
        
        # اختبار إضافة كاميرا بقناة
        test_camera_id = db.add_camera(
            name="اختبار القناة الجديدة",
            camera_type="dahua",
            ip_address="*************",
            port=554,
            username="admin",
            password="test123",
            channel=5,
            subtype=1,
            location_name="اختبار القنوات"
        )
        
        print(f"   ✅ تم إنشاء كاميرا اختبار: ID {test_camera_id}")
        print(f"   ✅ Test camera created: ID {test_camera_id}")
        
        # التحقق من الكاميرا الجديدة
        new_camera = db.get_camera_by_id(test_camera_id)
        if new_camera and new_camera.get('channel') == 5:
            print("   ✅ حقل القناة يعمل بشكل صحيح")
            print("   ✅ Channel field working correctly")
        else:
            print("   ❌ مشكلة في حقل القناة")
            print("   ❌ Issue with channel field")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        print(f"   ❌ Database error: {str(e)}")
        return False

def test_translations():
    """اختبار نظام الترجمة"""
    print("\n🌍 اختبار نظام الترجمة...")
    print("🌍 Testing translation system...")
    
    try:
        # اختبار النصوص الأساسية
        basic_texts = ['camera_monitor', 'dashboard', 'admin', 'channel', 'subtype']
        
        for text_key in basic_texts:
            ar_text = get_text(text_key, 'ar')
            en_text = get_text(text_key, 'en')
            
            if ar_text != text_key and en_text != text_key:
                print(f"   ✅ {text_key}: {ar_text} | {en_text}")
            else:
                print(f"   ⚠️ ترجمة مفقودة: {text_key}")
                print(f"   ⚠️ Missing translation: {text_key}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في نظام الترجمة: {str(e)}")
        print(f"   ❌ Translation system error: {str(e)}")
        return False

def test_api():
    """اختبار API"""
    print("\n🔌 اختبار API...")
    print("🔌 Testing API...")
    
    try:
        # محاولة الحصول على الكاميرات (بدون تسجيل دخول)
        api_response = requests.get('http://localhost:4040/api/cameras', timeout=5)
        
        # يجب أن يعيد 401 (غير مخول) أو يعيد للصفحة الرئيسية
        if api_response.status_code in [401, 302]:
            print("   ✅ حماية API تعمل (مطلوب تسجيل دخول)")
            print("   ✅ API protection working (login required)")
        else:
            print(f"   ⚠️ استجابة API غير متوقعة: {api_response.status_code}")
            print(f"   ⚠️ Unexpected API response: {api_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {str(e)}")
        print(f"   ❌ API test error: {str(e)}")
        return False

def test_camera_manager():
    """اختبار مدير الكاميرات"""
    print("\n📹 اختبار مدير الكاميرات...")
    print("📹 Testing camera manager...")
    
    try:
        from camera_manager import CameraManager
        
        camera_manager = CameraManager()
        
        # اختبار إنشاء مدير الكاميرات
        print("   ✅ تم إنشاء مدير الكاميرات")
        print("   ✅ Camera manager created")
        
        # اختبار الحصول على الكاميرات النشطة
        active_cameras = camera_manager.get_all_active_cameras()
        print(f"   📊 الكاميرات النشطة: {len(active_cameras)}")
        print(f"   📊 Active cameras: {len(active_cameras)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير الكاميرات: {str(e)}")
        print(f"   ❌ Camera manager error: {str(e)}")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("\n" + "=" * 60)
    print("📊 معلومات النظام")
    print("📊 System Information")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        cameras = db.get_cameras()
        
        print(f"🗄️ قاعدة البيانات: {len(cameras)} كاميرا")
        print(f"🗄️ Database: {len(cameras)} cameras")
        
        if cameras:
            print("\n📹 الكاميرات الموجودة:")
            print("📹 Existing cameras:")
            for camera in cameras:
                channel = camera.get('channel', 1)
                subtype = camera.get('subtype', 0)
                print(f"   • {camera['name']} - القناة: {channel}, النوع: {subtype}")
        
        print(f"\n🌐 الخادم: http://localhost:4040")
        print(f"🌐 Server: http://localhost:4040")
        print(f"🇸🇦 عربي: http://localhost:4040?language=ar")
        print(f"🇺🇸 English: http://localhost:4040?language=en")
        print(f"🔑 تسجيل الدخول: admin / admin123")
        print(f"🔑 Login: admin / admin123")
        
    except Exception as e:
        print(f"❌ خطأ في عرض معلومات النظام: {str(e)}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام مراقبة الكاميرات")
    print("🧪 Comprehensive Camera Monitoring System Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 5
    
    # اختبار الخادم
    if test_server():
        tests_passed += 1
    
    # اختبار قاعدة البيانات
    if test_database():
        tests_passed += 1
    
    # اختبار نظام الترجمة
    if test_translations():
        tests_passed += 1
    
    # اختبار API
    if test_api():
        tests_passed += 1
    
    # اختبار مدير الكاميرات
    if test_camera_manager():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار")
    print("📊 Test Results")
    print("=" * 60)
    print(f"✅ نجح: {tests_passed}/{total_tests} اختبار")
    print(f"✅ Passed: {tests_passed}/{total_tests} tests")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("🎉 All tests passed!")
        print("✅ النظام يعمل بشكل طبيعي مع دعم القنوات")
        print("✅ System working normally with channel support")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("⚠️ Some tests failed")
    
    # عرض معلومات النظام
    show_system_info()

if __name__ == "__main__":
    main()
