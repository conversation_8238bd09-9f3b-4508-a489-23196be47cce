#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب أداء نظام مراقبة الكاميرات
Performance monitor for camera monitoring system
"""

import time
import threading
import requests
import sqlite3
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        self.stats = {
            'requests_count': 0,
            'avg_response_time': 0,
            'db_queries': 0,
            'memory_usage': 0,
            'active_cameras': 0
        }
        
    def start_monitoring(self, duration=60):
        """بدء مراقبة الأداء لفترة محددة"""
        print(f"🔍 بدء مراقبة الأداء لمدة {duration} ثانية...")
        print(f"🔍 Starting performance monitoring for {duration} seconds...")
        
        self.monitoring = True
        start_time = time.time()
        
        # مراقبة الطلبات
        request_thread = threading.Thread(target=self._monitor_requests, args=(duration,))
        request_thread.start()
        
        # مراقبة قاعدة البيانات
        db_thread = threading.Thread(target=self._monitor_database, args=(duration,))
        db_thread.start()
        
        # مراقبة الذاكرة
        memory_thread = threading.Thread(target=self._monitor_memory, args=(duration,))
        memory_thread.start()
        
        # انتظار انتهاء المراقبة
        time.sleep(duration)
        self.monitoring = False
        
        # انتظار انتهاء جميع threads
        request_thread.join()
        db_thread.join()
        memory_thread.join()
        
        # عرض النتائج
        self._show_results()
        
    def _monitor_requests(self, duration):
        """مراقبة طلبات HTTP"""
        request_times = []
        request_count = 0
        
        while self.monitoring:
            try:
                start_time = time.time()
                response = requests.get('http://localhost:4040', timeout=5)
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = end_time - start_time
                    request_times.append(response_time)
                    request_count += 1
                    
                time.sleep(2)  # طلب كل ثانيتين
                
            except Exception as e:
                print(f"   ⚠️ خطأ في طلب HTTP: {str(e)}")
                time.sleep(5)
        
        # حساب المتوسط
        if request_times:
            self.stats['requests_count'] = request_count
            self.stats['avg_response_time'] = sum(request_times) / len(request_times)
    
    def _monitor_database(self, duration):
        """مراقبة أداء قاعدة البيانات"""
        query_count = 0
        
        while self.monitoring:
            try:
                start_time = time.time()
                
                conn = sqlite3.connect('camera_system.db')
                cursor = conn.cursor()
                
                # تنفيذ استعلامات اختبار
                cursor.execute("SELECT COUNT(*) FROM cameras WHERE is_active = 1")
                active_cameras = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
                cursor.execute("SELECT COUNT(*) FROM motion_events WHERE timestamp > datetime('now', '-1 hour')")
                
                conn.close()
                
                query_count += 3
                self.stats['active_cameras'] = active_cameras
                
                time.sleep(3)  # استعلام كل 3 ثوان
                
            except Exception as e:
                print(f"   ⚠️ خطأ في استعلام قاعدة البيانات: {str(e)}")
                time.sleep(5)
        
        self.stats['db_queries'] = query_count
    
    def _monitor_memory(self, duration):
        """مراقبة استخدام الذاكرة"""
        try:
            import psutil
            import os
            
            memory_readings = []
            
            while self.monitoring:
                try:
                    process = psutil.Process(os.getpid())
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    memory_readings.append(memory_mb)
                    
                    time.sleep(5)  # قراءة كل 5 ثوان
                    
                except Exception as e:
                    print(f"   ⚠️ خطأ في قراءة الذاكرة: {str(e)}")
                    time.sleep(10)
            
            if memory_readings:
                self.stats['memory_usage'] = sum(memory_readings) / len(memory_readings)
                
        except ImportError:
            print("   ⚠️ psutil غير مثبت - تخطي مراقبة الذاكرة")
            self.stats['memory_usage'] = 0
    
    def _show_results(self):
        """عرض نتائج المراقبة"""
        print("\n" + "=" * 60)
        print("📊 نتائج مراقبة الأداء")
        print("📊 Performance Monitoring Results")
        print("=" * 60)
        
        # طلبات HTTP
        print(f"🌐 طلبات HTTP:")
        print(f"   العدد: {self.stats['requests_count']}")
        print(f"   متوسط وقت الاستجابة: {self.stats['avg_response_time']:.3f} ثانية")
        
        if self.stats['avg_response_time'] < 0.5:
            print("   ✅ وقت الاستجابة ممتاز")
        elif self.stats['avg_response_time'] < 1.0:
            print("   ⚠️ وقت الاستجابة جيد")
        else:
            print("   ❌ وقت الاستجابة بطيء")
        
        # قاعدة البيانات
        print(f"\n🗄️ قاعدة البيانات:")
        print(f"   عدد الاستعلامات: {self.stats['db_queries']}")
        print(f"   الكاميرات النشطة: {self.stats['active_cameras']}")
        
        # الذاكرة
        if self.stats['memory_usage'] > 0:
            print(f"\n💾 الذاكرة:")
            print(f"   متوسط الاستخدام: {self.stats['memory_usage']:.1f} MB")
            
            if self.stats['memory_usage'] < 50:
                print("   ✅ استخدام الذاكرة ممتاز")
            elif self.stats['memory_usage'] < 100:
                print("   ⚠️ استخدام الذاكرة جيد")
            else:
                print("   ❌ استخدام الذاكرة مرتفع")
        
        # تقييم عام
        print(f"\n🎯 التقييم العام:")
        score = 0
        
        if self.stats['avg_response_time'] < 0.5:
            score += 30
        elif self.stats['avg_response_time'] < 1.0:
            score += 20
        else:
            score += 10
        
        if self.stats['memory_usage'] < 50 or self.stats['memory_usage'] == 0:
            score += 30
        elif self.stats['memory_usage'] < 100:
            score += 20
        else:
            score += 10
        
        if self.stats['requests_count'] > 10:
            score += 20
        elif self.stats['requests_count'] > 5:
            score += 15
        else:
            score += 10
        
        if self.stats['db_queries'] > 20:
            score += 20
        elif self.stats['db_queries'] > 10:
            score += 15
        else:
            score += 10
        
        if score >= 80:
            print("   🎉 الأداء ممتاز!")
            print("   🎉 Excellent performance!")
        elif score >= 60:
            print("   ✅ الأداء جيد")
            print("   ✅ Good performance")
        else:
            print("   ⚠️ الأداء يحتاج تحسين")
            print("   ⚠️ Performance needs improvement")
        
        print(f"   النقاط: {score}/100")

def quick_performance_check():
    """فحص سريع للأداء"""
    print("⚡ فحص سريع للأداء...")
    print("⚡ Quick performance check...")
    
    try:
        # اختبار سرعة الاستجابة
        start_time = time.time()
        response = requests.get('http://localhost:4040', timeout=10)
        response_time = time.time() - start_time
        
        print(f"   🌐 وقت الاستجابة: {response_time:.3f} ثانية")
        
        if response.status_code == 200:
            print("   ✅ الخادم يعمل بشكل طبيعي")
        else:
            print(f"   ❌ خطأ في الخادم: {response.status_code}")
        
        # اختبار قاعدة البيانات
        start_time = time.time()
        conn = sqlite3.connect('camera_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM cameras")
        db_time = time.time() - start_time
        conn.close()
        
        print(f"   🗄️ وقت استعلام قاعدة البيانات: {db_time:.3f} ثانية")
        
        if response_time < 1.0 and db_time < 0.1:
            print("   🎉 الأداء ممتاز!")
        elif response_time < 2.0 and db_time < 0.5:
            print("   ✅ الأداء جيد")
        else:
            print("   ⚠️ الأداء بطيء - قم بتشغيل تحسين_الأداء.py")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الفحص: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("📊 مراقب أداء نظام مراقبة الكاميرات")
    print("📊 Camera Monitoring System Performance Monitor")
    print("=" * 60)
    
    # فحص سريع أولاً
    if not quick_performance_check():
        print("\n❌ فشل الفحص السريع - تأكد من تشغيل النظام")
        return
    
    print(f"\n🔍 اختر نوع المراقبة:")
    print(f"1. فحص سريع (مكتمل)")
    print(f"2. مراقبة مفصلة (60 ثانية)")
    print(f"3. مراقبة مخصصة")
    
    try:
        choice = input("\nاختر (1-3): ").strip()
        
        if choice == "2":
            monitor = PerformanceMonitor()
            monitor.start_monitoring(60)
        elif choice == "3":
            duration = int(input("أدخل مدة المراقبة بالثواني: "))
            monitor = PerformanceMonitor()
            monitor.start_monitoring(duration)
        else:
            print("✅ تم الانتهاء من الفحص السريع")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المراقبة")
    except Exception as e:
        print(f"\n❌ خطأ: {str(e)}")

if __name__ == "__main__":
    main()
