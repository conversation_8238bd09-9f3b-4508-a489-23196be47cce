#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة حذف الكاميرا
Test camera deletion functionality
"""

import requests
import json
from database import DatabaseManager

def test_camera_deletion_api():
    """اختبار API حذف الكاميرا"""
    print("🗑️ اختبار API حذف الكاميرا...")
    print("🗑️ Testing Camera Deletion API...")
    
    try:
        db = DatabaseManager()
        
        # إنشاء كاميرا للاختبار
        test_camera_id = db.add_camera(
            name="كاميرا اختبار الحذف",
            camera_type="ip_camera",
            ip_address="*************",
            port=554,
            username="test",
            password="test123",
            channel=1,
            subtype=0,
            location_name="اختبار الحذف"
        )
        
        if test_camera_id:
            print(f"   ✅ تم إنشاء كاميرا اختبار: ID {test_camera_id}")
            print(f"   ✅ Test camera created: ID {test_camera_id}")
            
            # التحقق من وجود الكاميرا
            camera = db.get_camera_by_id(test_camera_id)
            if camera:
                print(f"   ✅ الكاميرا موجودة: {camera['name']}")
                print(f"   ✅ Camera exists: {camera['name']}")
            
            # اختبار حذف الكاميرا عبر قاعدة البيانات
            delete_success = db.delete_camera(test_camera_id)
            
            if delete_success:
                print("   ✅ حذف الكاميرا عبر قاعدة البيانات: نجح")
                print("   ✅ Camera deletion via database: Success")
                
                # التحقق من أن الكاميرا تم حذفها (soft delete)
                deleted_camera = db.get_camera_by_id(test_camera_id)
                if not deleted_camera:
                    print("   ✅ الكاميرا لم تعد ظاهرة في النتائج")
                    print("   ✅ Camera no longer appears in results")
                else:
                    print("   ⚠️ الكاميرا ما زالت موجودة (soft delete)")
                    print("   ⚠️ Camera still exists (soft delete)")
            else:
                print("   ❌ فشل في حذف الكاميرا")
                print("   ❌ Failed to delete camera")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار حذف الكاميرا: {str(e)}")
        print(f"   ❌ Camera deletion test error: {str(e)}")
        return False

def test_camera_deletion_endpoint():
    """اختبار endpoint حذف الكاميرا"""
    print("\n🔌 اختبار endpoint حذف الكاميرا...")
    print("🔌 Testing Camera Deletion Endpoint...")
    
    try:
        # محاولة حذف كاميرا بدون تسجيل دخول
        response = requests.delete('http://localhost:4040/api/cameras/999', timeout=5)
        
        if response.status_code in [401, 302]:
            print("   ✅ حماية endpoint: تعمل (مطلوب تسجيل دخول)")
            print("   ✅ Endpoint protection: Working (login required)")
        else:
            print(f"   ⚠️ استجابة غير متوقعة: {response.status_code}")
            print(f"   ⚠️ Unexpected response: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار endpoint: {str(e)}")
        print(f"   ❌ Endpoint test error: {str(e)}")
        return False

def test_admin_page_functionality():
    """اختبار وظائف صفحة الإدارة"""
    print("\n📄 اختبار وظائف صفحة الإدارة...")
    print("📄 Testing Admin Page Functionality...")
    
    try:
        # اختبار تحميل صفحة الإدارة
        response = requests.get('http://localhost:4040/admin?language=ar', timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # التحقق من وجود وظائف الحذف
            if 'deleteCamera' in content:
                print("   ✅ وظيفة deleteCamera موجودة في الصفحة")
                print("   ✅ deleteCamera function present in page")
            else:
                print("   ❌ وظيفة deleteCamera مفقودة")
                print("   ❌ deleteCamera function missing")
                return False
            
            # التحقق من وجود أزرار الحذف
            if 'fa-trash' in content:
                print("   ✅ أيقونات الحذف موجودة")
                print("   ✅ Delete icons present")
            else:
                print("   ❌ أيقونات الحذف مفقودة")
                print("   ❌ Delete icons missing")
                return False
            
            # التحقق من وجود رسائل التأكيد
            if 'confirm_delete_camera' in content:
                print("   ✅ رسائل تأكيد الحذف موجودة")
                print("   ✅ Delete confirmation messages present")
            else:
                print("   ❌ رسائل تأكيد الحذف مفقودة")
                print("   ❌ Delete confirmation messages missing")
                return False
            
        elif response.status_code == 302:
            print("   ✅ صفحة الإدارة محمية (إعادة توجيه)")
            print("   ✅ Admin page protected (redirect)")
        else:
            print(f"   ❌ خطأ في تحميل صفحة الإدارة: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار صفحة الإدارة: {str(e)}")
        print(f"   ❌ Admin page test error: {str(e)}")
        return False

def show_deletion_status():
    """عرض حالة وظيفة الحذف"""
    print("\n" + "=" * 60)
    print("🗑️ حالة وظيفة حذف الكاميرا")
    print("🗑️ Camera Deletion Function Status")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        cameras = db.get_cameras(active_only=False)
        
        print(f"📊 إحصائيات الكاميرات:")
        print(f"   إجمالي الكاميرات: {len(cameras)}")
        
        active_cameras = [c for c in cameras if c.get('is_active', True)]
        inactive_cameras = [c for c in cameras if not c.get('is_active', True)]
        
        print(f"   الكاميرات النشطة: {len(active_cameras)}")
        print(f"   الكاميرات المحذوفة (soft delete): {len(inactive_cameras)}")
        
        print(f"\n🔧 الوظائف المتاحة:")
        functions = [
            "✅ حذف الكاميرا عبر قاعدة البيانات",
            "✅ حذف الكاميرا عبر API",
            "✅ حذف الكاميرا عبر واجهة الإدارة",
            "✅ تأكيد قبل الحذف",
            "✅ رسائل نجاح/فشل",
            "✅ حذف آمن (soft delete)",
            "✅ حماية من الحذف غير المصرح"
        ]
        
        for func in functions:
            print(f"   {func}")
        
        print(f"\n🌐 كيفية استخدام وظيفة الحذف:")
        print(f"   1. اذهب إلى: http://localhost:4040/admin?language=ar")
        print(f"   2. سجل دخول كمدير: admin / admin123")
        print(f"   3. انقر على القائمة المنسدلة ⋮ بجانب الكاميرا")
        print(f"   4. اختر 'حذف الكاميرا' 🗑️")
        print(f"   5. أكد الحذف في النافذة المنبثقة")
        
        if inactive_cameras:
            print(f"\n🗑️ الكاميرات المحذوفة:")
            for camera in inactive_cameras:
                print(f"   • {camera['name']} (ID: {camera['id']})")
        
    except Exception as e:
        print(f"❌ خطأ في عرض حالة الحذف: {str(e)}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لوظيفة حذف الكاميرا")
    print("🧪 Comprehensive Camera Deletion Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار حذف الكاميرا عبر قاعدة البيانات
    if test_camera_deletion_api():
        tests_passed += 1
    
    # اختبار endpoint الحذف
    if test_camera_deletion_endpoint():
        tests_passed += 1
    
    # اختبار وظائف صفحة الإدارة
    if test_admin_page_functionality():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وظيفة حذف الكاميرا")
    print("📊 Camera Deletion Function Test Results")
    print("=" * 60)
    print(f"✅ نجح: {tests_passed}/{total_tests} اختبار")
    print(f"✅ Passed: {tests_passed}/{total_tests} tests")
    
    if tests_passed == total_tests:
        print("🎉 جميع اختبارات وظيفة حذف الكاميرا نجحت!")
        print("🎉 All camera deletion function tests passed!")
        print("✅ وظيفة الحذف تعمل بشكل مثالي")
        print("✅ Deletion function working perfectly")
    else:
        print("⚠️ بعض اختبارات وظيفة الحذف فشلت")
        print("⚠️ Some deletion function tests failed")
    
    # عرض حالة وظيفة الحذف
    show_deletion_status()

if __name__ == "__main__":
    main()
