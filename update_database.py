#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإضافة حقول القناة
Database update script to add channel fields
"""

import sqlite3
import os
from config import Config

def update_database():
    """تحديث قاعدة البيانات لإضافة حقول القناة"""
    print("🔄 بدء تحديث قاعدة البيانات...")
    print("🔄 Starting database update...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(Config.DATABASE_PATH)
        cursor = conn.cursor()
        
        # التحقق من وجود عمود channel
        cursor.execute("PRAGMA table_info(cameras)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'channel' not in columns:
            print("➕ إضافة عمود القناة...")
            print("➕ Adding channel column...")
            cursor.execute("ALTER TABLE cameras ADD COLUMN channel INTEGER DEFAULT 1")
            
        if 'subtype' not in columns:
            print("➕ إضافة عمود النوع الفرعي...")
            print("➕ Adding subtype column...")
            cursor.execute("ALTER TABLE cameras ADD COLUMN subtype INTEGER DEFAULT 0")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("PRAGMA table_info(cameras)")
        updated_columns = [column[1] for column in cursor.fetchall()]
        
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        print("✅ Database updated successfully!")
        print(f"📊 الأعمدة الحالية: {', '.join(updated_columns)}")
        print(f"📊 Current columns: {', '.join(updated_columns)}")
        
        # عرض الكاميرات الموجودة
        cursor.execute("SELECT id, name, channel, subtype FROM cameras")
        cameras = cursor.fetchall()
        
        if cameras:
            print(f"\n📹 الكاميرات الموجودة ({len(cameras)}):")
            print(f"📹 Existing cameras ({len(cameras)}):")
            for camera in cameras:
                channel = camera[2] if camera[2] is not None else 1
                subtype = camera[3] if camera[3] is not None else 0
                print(f"   ID: {camera[0]}, الاسم: {camera[1]}, القناة: {channel}, النوع: {subtype}")
        else:
            print("\n📹 لا توجد كاميرات في قاعدة البيانات")
            print("📹 No cameras in database")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        print(f"❌ Database update error: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🧪 اختبار قاعدة البيانات...")
    print("🧪 Testing database...")
    
    try:
        from database import DatabaseManager
        
        db = DatabaseManager()
        
        # اختبار إضافة كاميرا بقناة
        test_camera_id = db.add_camera(
            name="كاميرا اختبار القناة",
            camera_type="dahua",
            ip_address="*************",
            port=554,
            username="admin",
            password="test123",
            channel=3,
            subtype=1,
            location_name="اختبار"
        )
        
        print(f"✅ تم إنشاء كاميرا اختبار بالمعرف: {test_camera_id}")
        print(f"✅ Test camera created with ID: {test_camera_id}")
        
        # استرجاع الكاميرا للتحقق
        camera = db.get_camera_by_id(test_camera_id)
        if camera:
            print(f"📹 تفاصيل الكاميرا:")
            print(f"   الاسم: {camera['name']}")
            print(f"   القناة: {camera.get('channel', 'غير محدد')}")
            print(f"   النوع الفرعي: {camera.get('subtype', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        print(f"❌ Database test error: {str(e)}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🗄️ أداة تحديث قاعدة البيانات")
    print("🗄️ Database Update Tool")
    print("=" * 60)
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(Config.DATABASE_PATH):
        print("❌ قاعدة البيانات غير موجودة!")
        print("❌ Database not found!")
        print("💡 شغل النظام أولاً لإنشاء قاعدة البيانات")
        print("💡 Run the system first to create the database")
        return
    
    # تحديث قاعدة البيانات
    if update_database():
        # اختبار قاعدة البيانات
        if test_database():
            print("\n" + "=" * 60)
            print("🎉 تم تحديث قاعدة البيانات بنجاح!")
            print("🎉 Database updated successfully!")
            print("✅ النظام جاهز للعمل مع دعم القنوات")
            print("✅ System ready to work with channel support")
            print("=" * 60)
        else:
            print("\n❌ فشل في اختبار قاعدة البيانات")
            print("❌ Database test failed")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
        print("❌ Database update failed")

if __name__ == "__main__":
    main()
