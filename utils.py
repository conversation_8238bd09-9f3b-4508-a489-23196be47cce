import cv2
import numpy as np
import smtplib
import os
import threading
import time
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from email.mime.image import MimeImage
import requests
from config import Config

class MotionDetector:
    def __init__(self, threshold=Config.MOTION_THRESHOLD, min_area=Config.MOTION_MIN_AREA):
        self.threshold = threshold
        self.min_area = min_area
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2()
        self.previous_frame = None
        
    def detect_motion(self, frame):
        """Detect motion in the given frame"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = cv2.GaussianBlur(gray, (21, 21), 0)
        
        if self.previous_frame is None:
            self.previous_frame = gray
            return False, 0, []
        
        # Calculate frame difference
        frame_delta = cv2.absdiff(self.previous_frame, gray)
        thresh = cv2.threshold(frame_delta, self.threshold, 255, cv2.THRESH_BINARY)[1]
        thresh = cv2.dilate(thresh, None, iterations=2)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        motion_detected = False
        motion_areas = []
        total_motion_area = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                motion_detected = True
                total_motion_area += area
                x, y, w, h = cv2.boundingRect(contour)
                motion_areas.append((x, y, w, h))
        
        self.previous_frame = gray
        
        # Calculate confidence based on motion area
        confidence = min(total_motion_area / 10000 * 100, 100)
        
        return motion_detected, confidence, motion_areas
    
    def draw_motion_areas(self, frame, motion_areas):
        """Draw rectangles around motion areas"""
        for (x, y, w, h) in motion_areas:
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
        return frame

class VideoRecorder:
    def __init__(self, output_path=Config.RECORDING_PATH):
        self.output_path = output_path
        self.active_recordings = {}
        self.recording_lock = threading.Lock()
        
        # Create output directory if it doesn't exist
        if not os.path.exists(output_path):
            os.makedirs(output_path)
    
    def start_recording(self, camera_id, frame_size=(640, 480), fps=20):
        """Start recording for a camera"""
        with self.recording_lock:
            if camera_id in self.active_recordings:
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{camera_id}_{timestamp}.avi"
            filepath = os.path.join(self.output_path, filename)
            
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            writer = cv2.VideoWriter(filepath, fourcc, fps, frame_size)
            
            self.active_recordings[camera_id] = {
                'writer': writer,
                'filepath': filepath,
                'start_time': datetime.now()
            }
            
            return True
    
    def stop_recording(self, camera_id):
        """Stop recording for a camera"""
        with self.recording_lock:
            if camera_id not in self.active_recordings:
                return None
            
            recording = self.active_recordings[camera_id]
            recording['writer'].release()
            
            filepath = recording['filepath']
            duration = datetime.now() - recording['start_time']
            
            del self.active_recordings[camera_id]
            
            return {
                'filepath': filepath,
                'duration': duration.total_seconds()
            }
    
    def write_frame(self, camera_id, frame):
        """Write a frame to the recording"""
        with self.recording_lock:
            if camera_id in self.active_recordings:
                self.active_recordings[camera_id]['writer'].write(frame)
    
    def is_recording(self, camera_id):
        """Check if camera is currently recording"""
        return camera_id in self.active_recordings
    
    def cleanup_old_recordings(self, max_days=Config.MAX_RECORDING_DAYS):
        """Clean up recordings older than max_days"""
        cutoff_date = datetime.now() - timedelta(days=max_days)
        
        for filename in os.listdir(self.output_path):
            filepath = os.path.join(self.output_path, filename)
            if os.path.isfile(filepath):
                file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                if file_time < cutoff_date:
                    try:
                        os.remove(filepath)
                        print(f"Deleted old recording: {filename}")
                    except Exception as e:
                        print(f"Error deleting {filename}: {str(e)}")

class NotificationManager:
    def __init__(self):
        self.email_config = {
            'server': Config.MAIL_SERVER,
            'port': Config.MAIL_PORT,
            'username': Config.MAIL_USERNAME,
            'password': Config.MAIL_PASSWORD,
            'use_tls': Config.MAIL_USE_TLS
        }
        self.whatsapp_api_key = Config.WHATSAPP_API_KEY
    
    def send_email_notification(self, to_email, subject, message, image_path=None):
        """Send email notification"""
        try:
            if not all([self.email_config['username'], self.email_config['password']]):
                print("Email configuration not complete")
                return False
            
            msg = MimeMultipart()
            msg['From'] = self.email_config['username']
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add text message
            msg.attach(MimeText(message, 'plain'))
            
            # Add image if provided
            if image_path and os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    img_data = f.read()
                    image = MimeImage(img_data)
                    image.add_header('Content-Disposition', 'attachment', filename=os.path.basename(image_path))
                    msg.attach(image)
            
            # Send email
            server = smtplib.SMTP(self.email_config['server'], self.email_config['port'])
            if self.email_config['use_tls']:
                server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            print(f"Error sending email: {str(e)}")
            return False
    
    def send_whatsapp_notification(self, phone_number, message):
        """Send WhatsApp notification (requires WhatsApp Business API)"""
        try:
            if not self.whatsapp_api_key:
                print("WhatsApp API key not configured")
                return False
            
            # This is a placeholder for WhatsApp API integration
            # You would need to implement the actual API calls based on your provider
            # (e.g., Twilio, WhatsApp Business API, etc.)
            
            print(f"WhatsApp notification sent to {phone_number}: {message}")
            return True
            
        except Exception as e:
            print(f"Error sending WhatsApp message: {str(e)}")
            return False
    
    def send_motion_alert(self, camera_name, confidence, image_path=None):
        """Send motion detection alert"""
        subject = f"Motion Detected - {camera_name}"
        message = f"""
        Motion has been detected on camera: {camera_name}
        
        Detection Details:
        - Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        - Confidence: {confidence:.1f}%
        - Camera: {camera_name}
        
        Please check the camera feed for more details.
        
        Camera Monitoring System
        """
        
        # Send email notification (you would configure recipient emails)
        # self.send_email_notification('<EMAIL>', subject, message, image_path)
        
        print(f"Motion alert: {subject}")

class SystemHealthMonitor:
    def __init__(self):
        self.start_time = datetime.now()
        self.stats = {
            'total_cameras': 0,
            'online_cameras': 0,
            'offline_cameras': 0,
            'error_cameras': 0,
            'total_motion_events': 0,
            'system_uptime': 0
        }
    
    def update_camera_stats(self, cameras):
        """Update camera statistics"""
        self.stats['total_cameras'] = len(cameras)
        self.stats['online_cameras'] = len([c for c in cameras if c.get('status') == 'online'])
        self.stats['offline_cameras'] = len([c for c in cameras if c.get('status') == 'offline'])
        self.stats['error_cameras'] = len([c for c in cameras if c.get('status') == 'error'])
    
    def get_system_uptime(self):
        """Get system uptime in seconds"""
        uptime = datetime.now() - self.start_time
        return uptime.total_seconds()
    
    def get_memory_usage(self):
        """Get memory usage information"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used
            }
        except ImportError:
            return None
    
    def get_cpu_usage(self):
        """Get CPU usage percentage"""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            return None
    
    def get_disk_usage(self):
        """Get disk usage for recordings directory"""
        try:
            import psutil
            disk = psutil.disk_usage(Config.RECORDING_PATH)
            return {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }
        except:
            return None
    
    def get_health_report(self):
        """Get comprehensive system health report"""
        return {
            'uptime_seconds': self.get_system_uptime(),
            'camera_stats': self.stats,
            'memory_usage': self.get_memory_usage(),
            'cpu_usage': self.get_cpu_usage(),
            'disk_usage': self.get_disk_usage(),
            'timestamp': datetime.now().isoformat()
        }

class ImageProcessor:
    @staticmethod
    def resize_image(image, max_width=800, max_height=600):
        """Resize image while maintaining aspect ratio"""
        height, width = image.shape[:2]
        
        # Calculate scaling factor
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h)
        
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        return image
    
    @staticmethod
    def add_timestamp(image, timestamp=None):
        """Add timestamp overlay to image"""
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (255, 255, 255)
        thickness = 2
        
        # Get text size
        text_size = cv2.getTextSize(timestamp, font, font_scale, thickness)[0]
        
        # Position at bottom-right corner
        x = image.shape[1] - text_size[0] - 10
        y = image.shape[0] - 10
        
        # Add black background for better readability
        cv2.rectangle(image, (x-5, y-text_size[1]-5), (x+text_size[0]+5, y+5), (0, 0, 0), -1)
        cv2.putText(image, timestamp, (x, y), font, font_scale, color, thickness)
        
        return image
    
    @staticmethod
    def save_snapshot(image, camera_id, timestamp=None):
        """Save camera snapshot"""
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        filename = f"snapshot_camera_{camera_id}_{timestamp}.jpg"
        filepath = os.path.join(Config.RECORDING_PATH, filename)
        
        # Add timestamp to image
        image_with_timestamp = ImageProcessor.add_timestamp(image.copy())
        
        # Save image
        cv2.imwrite(filepath, image_with_timestamp)
        return filepath

# Initialize global instances
motion_detector = MotionDetector()
video_recorder = VideoRecorder()
notification_manager = NotificationManager()
health_monitor = SystemHealthMonitor()

# Cleanup thread for old recordings
def cleanup_thread():
    while True:
        try:
            video_recorder.cleanup_old_recordings()
            time.sleep(86400)  # Run once per day
        except Exception as e:
            print(f"Error in cleanup thread: {str(e)}")
            time.sleep(3600)  # Wait 1 hour before retrying

# Start cleanup thread
cleanup_thread_instance = threading.Thread(target=cleanup_thread, daemon=True)
cleanup_thread_instance.start()
