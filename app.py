from flask import Flask, render_template, request, jsonify, Response, redirect, url_for, flash, session, g
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
import cv2
import os

from config import Config
from database import DatabaseManager
from camera_manager import CameraManager
from auth import AuthManager
from translations import get_text, get_user_language, is_rtl_language

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# Initialize managers
db_manager = DatabaseManager()
camera_manager = CameraManager()
auth_manager = AuthManager()

@login_manager.user_loader
def load_user(user_id):
    return auth_manager.get_user_by_id(int(user_id))

@app.before_request
def before_request():
    """Set up language and direction for each request"""
    g.lang = get_user_language(request)
    g.is_rtl = is_rtl_language(g.lang)
    g.get_text = lambda key: get_text(key, g.lang)

    # Set session language if not set
    if 'language' not in session and g.lang:
        session['language'] = g.lang

# Routes
@app.route('/')
@login_required
def index():
    """Main dashboard"""
    if not current_user.can_view_cameras():
        flash('You do not have permission to view cameras.', 'error')
        return redirect(url_for('login'))
    
    cameras = db_manager.get_cameras()
    return render_template('index.html', cameras=cameras, user=current_user)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = auth_manager.authenticate_user(username, password)
        if user:
            login_user(user, remember=True)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash(g.get_text('invalid_credentials'), 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash(g.get_text('logged_out'), 'info')
    return redirect(url_for('login'))

@app.route('/set_language/<language>')
def set_language(language):
    """Set user language preference"""
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.route('/users')
@login_required
def users():
    """User management page"""
    if not current_user.is_admin():
        flash(g.get_text('no_permission'), 'error')
        return redirect(url_for('index'))

    users = db_manager.get_all_users()
    user_stats = db_manager.get_user_stats()
    return render_template('users.html', users=users, user_stats=user_stats)

@app.route('/admin')
@login_required
def admin():
    """Admin panel"""
    if not current_user.can_manage_cameras():
        flash(g.get_text('no_permission'), 'error')
        return redirect(url_for('index'))

    cameras = db_manager.get_cameras(active_only=False)
    return render_template('admin.html', cameras=cameras, user=current_user)

@app.route('/camera/<int:camera_id>')
@login_required
def camera_detail(camera_id):
    """Individual camera view"""
    if not current_user.can_view_cameras():
        flash(g.get_text('no_permission'), 'error')
        return redirect(url_for('login'))

    camera = db_manager.get_camera_by_id(camera_id)
    if not camera:
        flash(g.get_text('camera_not_found'), 'error')
        return redirect(url_for('index'))

    return render_template('camera_detail.html', camera=camera, user=current_user)

@app.route('/video_feed/<int:camera_id>')
@login_required
def video_feed(camera_id):
    """Video streaming route"""
    if not current_user.can_view_cameras():
        return Response('Unauthorized', status=401)
    
    def generate():
        while True:
            frame = camera_manager.get_camera_frame(camera_id)
            if frame:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
            else:
                # Return a placeholder image if camera is not available
                placeholder = create_placeholder_frame(f"Camera {camera_id} Offline")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + placeholder + b'\r\n')
    
    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/api/cameras', methods=['GET'])
@login_required
def api_get_cameras():
    """API endpoint to get all cameras"""
    if not current_user.can_view_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    cameras = db_manager.get_cameras()
    return jsonify(cameras)

@app.route('/api/cameras', methods=['POST'])
@login_required
def api_add_camera():
    """API endpoint to add a new camera"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    data = request.get_json()
    
    required_fields = ['name', 'camera_type']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    try:
        camera_id = db_manager.add_camera(
            name=data['name'],
            camera_type=data['camera_type'],
            rtsp_url=data.get('rtsp_url'),
            ip_address=data.get('ip_address'),
            port=data.get('port'),
            username=data.get('username'),
            password=data.get('password'),
            channel=data.get('channel', 1),
            subtype=data.get('subtype', 0),
            latitude=data.get('latitude'),
            longitude=data.get('longitude'),
            location_name=data.get('location_name')
        )
        
        db_manager.log_system_event('INFO', f'Camera {data["name"]} added', current_user.id)
        return jsonify({'success': True, 'camera_id': camera_id})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/test', methods=['POST'])
@login_required
def api_test_camera(camera_id):
    """API endpoint to test camera connection"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    camera = db_manager.get_camera_by_id(camera_id)
    if not camera:
        return jsonify({'error': 'Camera not found'}), 404
    
    try:
        is_working = camera_manager.test_camera_connection(camera)
        return jsonify({'working': is_working})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/start', methods=['POST'])
@login_required
def api_start_camera(camera_id):
    """API endpoint to start camera stream"""
    if not current_user.can_view_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        success = camera_manager.start_camera_stream(camera_id)
        return jsonify({'success': success})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/stop', methods=['POST'])
@login_required
def api_stop_camera(camera_id):
    """API endpoint to stop camera stream"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        camera_manager.stop_camera_stream(camera_id)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_placeholder_frame(text):
    """Create a placeholder frame when camera is offline"""
    import numpy as np

    # Create a black image
    img = np.zeros((480, 640, 3), dtype=np.uint8)

    # Add text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    color = (255, 255, 255)
    thickness = 2

    # Get text size
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

    # Calculate position to center the text
    text_x = (img.shape[1] - text_size[0]) // 2
    text_y = (img.shape[0] + text_size[1]) // 2

    cv2.putText(img, text, (text_x, text_y), font, font_scale, color, thickness)

    # Encode as JPEG
    _, buffer = cv2.imencode('.jpg', img)
    return buffer.tobytes()

# User Management API Routes
@app.route('/api/users', methods=['GET', 'POST'])
@login_required
def api_users():
    """API for user management"""
    if not current_user.is_admin():
        return jsonify({'error': g.get_text('no_permission')}), 403

    if request.method == 'GET':
        users = db_manager.get_all_users()
        return jsonify(users)

    elif request.method == 'POST':
        data = request.get_json()

        # Validate required fields
        if not data.get('username') or not data.get('password'):
            return jsonify({'error': g.get_text('missing_required_field')}), 400

        # Check if username already exists
        existing_user = auth_manager.get_user_by_username(data['username'])
        if existing_user:
            return jsonify({'error': g.get_text('user_already_exists')}), 400

        try:
            user_id = db_manager.create_user(
                username=data['username'],
                password=data['password'],
                role=data.get('role', 1),
                email=data.get('email', '')
            )

            db_manager.log_system_event('INFO', f"User created: {data['username']}", current_user.id)
            return jsonify({'success': True, 'user_id': user_id, 'message': g.get_text('user_created')})

        except Exception as e:
            return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT', 'DELETE'])
@login_required
def api_user_detail(user_id):
    """API for individual user management"""
    if not current_user.is_admin():
        return jsonify({'error': g.get_text('no_permission')}), 403

    if request.method == 'PUT':
        data = request.get_json()

        try:
            success = db_manager.update_user(
                user_id=user_id,
                username=data.get('username'),
                email=data.get('email'),
                role=data.get('role'),
                is_active=data.get('is_active')
            )

            if success:
                db_manager.log_system_event('INFO', f"User updated: ID {user_id}", current_user.id)
                return jsonify({'success': True, 'message': g.get_text('user_updated')})
            else:
                return jsonify({'error': g.get_text('user_not_found')}), 404

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'DELETE':
        # Prevent self-deletion
        if user_id == current_user.id:
            return jsonify({'error': g.get_text('cannot_delete_self')}), 400

        # Prevent deletion of main admin (ID 1)
        if user_id == 1:
            return jsonify({'error': g.get_text('cannot_delete_admin')}), 400

        try:
            success = db_manager.delete_user(user_id)

            if success:
                db_manager.log_system_event('INFO', f"User deleted: ID {user_id}", current_user.id)
                return jsonify({'success': True, 'message': g.get_text('user_deleted')})
            else:
                return jsonify({'error': g.get_text('user_not_found')}), 404

        except Exception as e:
            return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>/password', methods=['POST'])
@login_required
def api_change_password(user_id):
    """API for changing user password"""
    if not current_user.is_admin() and user_id != current_user.id:
        return jsonify({'error': g.get_text('no_permission')}), 403

    data = request.get_json()
    new_password = data.get('new_password')

    if not new_password:
        return jsonify({'error': g.get_text('missing_required_field')}), 400

    if len(new_password) < 6:
        return jsonify({'error': g.get_text('weak_password')}), 400

    try:
        from werkzeug.security import generate_password_hash
        password_hash = generate_password_hash(new_password)

        success = db_manager.change_user_password(user_id, password_hash)

        if success:
            db_manager.log_system_event('INFO', f"Password changed for user ID {user_id}", current_user.id)
            return jsonify({'success': True, 'message': g.get_text('password_changed')})
        else:
            return jsonify({'error': g.get_text('user_not_found')}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>', methods=['PUT', 'DELETE'])
@login_required
def api_camera_detail(camera_id):
    """API endpoint for camera management"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401

    if request.method == 'PUT':
        data = request.get_json()

        try:
            success = db_manager.update_camera(
                camera_id=camera_id,
                name=data.get('name'),
                camera_type=data.get('camera_type'),
                ip_address=data.get('ip_address'),
                port=data.get('port'),
                username=data.get('username'),
                password=data.get('password'),
                channel=data.get('channel'),
                subtype=data.get('subtype'),
                latitude=data.get('latitude'),
                longitude=data.get('longitude'),
                location_name=data.get('location_name')
            )

            if success:
                db_manager.log_system_event('INFO', f'Camera {camera_id} updated', current_user.id)
                return jsonify({'success': True, 'message': g.get_text('camera_updated')})
            else:
                return jsonify({'error': g.get_text('camera_not_found')}), 404

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'DELETE':
        try:
            success = db_manager.delete_camera(camera_id)

            if success:
                db_manager.log_system_event('INFO', f'Camera {camera_id} deleted', current_user.id)
                return jsonify({'success': True, 'message': g.get_text('camera_deleted')})
            else:
                return jsonify({'error': g.get_text('camera_not_found')}), 404

        except Exception as e:
            return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/toggle', methods=['POST'])
@login_required
def api_toggle_camera(camera_id):
    """API endpoint to toggle camera active status"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.get_json()
    active = data.get('active', True)

    try:
        success = db_manager.toggle_camera_active(camera_id, active)

        if success:
            status = 'activated' if active else 'deactivated'
            db_manager.log_system_event('INFO', f'Camera {camera_id} {status}', current_user.id)
            return jsonify({'success': True, 'message': g.get_text(f'camera_{status}')})
        else:
            return jsonify({'error': g.get_text('camera_not_found')}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/restart', methods=['POST'])
@login_required
def api_restart_camera(camera_id):
    """API endpoint to restart camera"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Stop and start camera
        camera_manager.stop_camera_stream(camera_id)
        success = camera_manager.start_camera_stream(camera_id)

        if success:
            db_manager.log_system_event('INFO', f'Camera {camera_id} restarted', current_user.id)
            return jsonify({'success': True, 'message': g.get_text('camera_restarted')})
        else:
            return jsonify({'error': g.get_text('camera_restart_failed')}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/record', methods=['POST'])
@login_required
def api_record_camera(camera_id):
    """API endpoint to start recording"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        success = camera_manager.start_recording(camera_id)

        if success:
            db_manager.log_system_event('INFO', f'Recording started for camera {camera_id}', current_user.id)
            return jsonify({'success': True, 'message': g.get_text('recording_started')})
        else:
            return jsonify({'error': g.get_text('recording_failed')}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/snapshot', methods=['POST'])
@login_required
def api_snapshot_camera(camera_id):
    """API endpoint to take snapshot"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        snapshot_path = camera_manager.take_snapshot(camera_id)

        if snapshot_path:
            db_manager.log_system_event('INFO', f'Snapshot taken for camera {camera_id}', current_user.id)
            return jsonify({'success': True, 'message': g.get_text('snapshot_taken'), 'path': snapshot_path})
        else:
            return jsonify({'error': g.get_text('snapshot_failed')}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Create recordings directory if it doesn't exist
    if not os.path.exists(Config.RECORDING_PATH):
        os.makedirs(Config.RECORDING_PATH)
    
    # Start the Flask app
    print(f"Starting Camera Monitoring System on http://{Config.HOST}:{Config.PORT}")
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG, threaded=True)
