from flask import Flask, render_template, request, jsonify, Response, redirect, url_for, flash, session, g
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
import cv2
import os
import numpy as np

from config import Config
from database import DatabaseManager
from camera_manager import <PERSON>Manager
from auth import AuthManager
from translations import get_text, get_user_language, is_rtl_language

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# Initialize managers
db_manager = DatabaseManager()
camera_manager = CameraManager()
auth_manager = AuthManager()

@login_manager.user_loader
def load_user(user_id):
    return auth_manager.get_user_by_id(int(user_id))

@app.before_request
def before_request():
    """Set up language and direction for each request"""
    g.lang = get_user_language(request)
    g.is_rtl = is_rtl_language(g.lang)
    g.get_text = lambda key: get_text(key, g.lang)

    # Set session language if not set
    if 'language' not in session and g.lang:
        session['language'] = g.lang

# Routes
@app.route('/')
@login_required
def index():
    """Main dashboard"""
    if not current_user.can_view_cameras():
        flash('You do not have permission to view cameras.', 'error')
        return redirect(url_for('login'))
    
    cameras = db_manager.get_cameras()
    return render_template('index.html', cameras=cameras, user=current_user)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = auth_manager.authenticate_user(username, password)
        if user:
            login_user(user, remember=True)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash(g.get_text('invalid_credentials'), 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash(g.get_text('logged_out'), 'info')
    return redirect(url_for('login'))

@app.route('/set_language/<language>')
def set_language(language):
    """Set user language preference"""
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.route('/admin')
@login_required
def admin():
    """Admin panel"""
    if not current_user.can_manage_cameras():
        flash(g.get_text('no_permission'), 'error')
        return redirect(url_for('index'))

    cameras = db_manager.get_cameras(active_only=False)
    return render_template('admin.html', cameras=cameras, user=current_user)

@app.route('/camera/<int:camera_id>')
@login_required
def camera_detail(camera_id):
    """Individual camera view"""
    if not current_user.can_view_cameras():
        flash(g.get_text('no_permission'), 'error')
        return redirect(url_for('login'))

    camera = db_manager.get_camera_by_id(camera_id)
    if not camera:
        flash(g.get_text('camera_not_found'), 'error')
        return redirect(url_for('index'))

    return render_template('camera_detail.html', camera=camera, user=current_user)

@app.route('/video_feed/<int:camera_id>')
@login_required
def video_feed(camera_id):
    """Video streaming route"""
    if not current_user.can_view_cameras():
        return Response('Unauthorized', status=401)
    
    def generate():
        while True:
            frame = camera_manager.get_camera_frame(camera_id)
            if frame:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
            else:
                # Return a placeholder image if camera is not available
                placeholder = create_placeholder_frame(f"Camera {camera_id} Offline")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + placeholder + b'\r\n')
    
    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/api/cameras', methods=['GET'])
@login_required
def api_get_cameras():
    """API endpoint to get all cameras"""
    if not current_user.can_view_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    cameras = db_manager.get_cameras()
    return jsonify(cameras)

@app.route('/api/cameras', methods=['POST'])
@login_required
def api_add_camera():
    """API endpoint to add a new camera"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    data = request.get_json()
    
    required_fields = ['name', 'camera_type']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    try:
        camera_id = db_manager.add_camera(
            name=data['name'],
            camera_type=data['camera_type'],
            rtsp_url=data.get('rtsp_url'),
            ip_address=data.get('ip_address'),
            port=data.get('port'),
            username=data.get('username'),
            password=data.get('password'),
            latitude=data.get('latitude'),
            longitude=data.get('longitude'),
            location_name=data.get('location_name')
        )
        
        db_manager.log_system_event('INFO', f'Camera {data["name"]} added', current_user.id)
        return jsonify({'success': True, 'camera_id': camera_id})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/test', methods=['POST'])
@login_required
def api_test_camera(camera_id):
    """API endpoint to test camera connection"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    camera = db_manager.get_camera_by_id(camera_id)
    if not camera:
        return jsonify({'error': 'Camera not found'}), 404
    
    try:
        is_working = camera_manager.test_camera_connection(camera)
        return jsonify({'working': is_working})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/start', methods=['POST'])
@login_required
def api_start_camera(camera_id):
    """API endpoint to start camera stream"""
    if not current_user.can_view_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        success = camera_manager.start_camera_stream(camera_id)
        return jsonify({'success': success})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cameras/<int:camera_id>/stop', methods=['POST'])
@login_required
def api_stop_camera(camera_id):
    """API endpoint to stop camera stream"""
    if not current_user.can_manage_cameras():
        return jsonify({'error': 'Unauthorized'}), 401
    
    try:
        camera_manager.stop_camera_stream(camera_id)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_placeholder_frame(text):
    """Create a placeholder frame when camera is offline"""
    import numpy as np

    # Create a black image
    img = np.zeros((480, 640, 3), dtype=np.uint8)

    # Add text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    color = (255, 255, 255)
    thickness = 2

    # Get text size
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

    # Calculate position to center the text
    text_x = (img.shape[1] - text_size[0]) // 2
    text_y = (img.shape[0] + text_size[1]) // 2

    cv2.putText(img, text, (text_x, text_y), font, font_scale, color, thickness)

    # Encode as JPEG
    _, buffer = cv2.imencode('.jpg', img)
    return buffer.tobytes()

if __name__ == '__main__':
    # Create recordings directory if it doesn't exist
    if not os.path.exists(Config.RECORDING_PATH):
        os.makedirs(Config.RECORDING_PATH)
    
    # Start the Flask app
    print(f"Starting Camera Monitoring System on http://{Config.HOST}:{Config.PORT}")
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG, threaded=True)
