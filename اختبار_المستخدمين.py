#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المستخدمين
User Management System Test
"""

import requests
import json
from database import DatabaseManager

def test_user_management():
    """اختبار نظام إدارة المستخدمين"""
    print("👥 اختبار نظام إدارة المستخدمين...")
    print("👥 Testing User Management System...")
    
    try:
        db = DatabaseManager()
        
        # اختبار الحصول على جميع المستخدمين
        users = db.get_all_users()
        print(f"   📊 عدد المستخدمين: {len(users)}")
        print(f"   📊 Number of users: {len(users)}")
        
        # اختبار إحصائيات المستخدمين
        stats = db.get_user_stats()
        print(f"   📈 إحصائيات المستخدمين:")
        print(f"      المجموع: {stats['total']}")
        print(f"      تسجيلات دخول حديثة: {stats['recent_logins']}")
        print(f"      حسب الدور: {stats['by_role']}")
        
        # اختبار إنشاء مستخدم جديد
        test_user_id = db.create_user(
            username="اختبار_مستخدم",
            password="test123456",
            role=2,
            email="<EMAIL>"
        )
        
        if test_user_id:
            print(f"   ✅ تم إنشاء مستخدم اختبار: ID {test_user_id}")
            print(f"   ✅ Test user created: ID {test_user_id}")
            
            # اختبار تحديث المستخدم
            update_success = db.update_user(
                user_id=test_user_id,
                email="<EMAIL>",
                role=1
            )
            
            if update_success:
                print("   ✅ تم تحديث المستخدم بنجاح")
                print("   ✅ User updated successfully")
            
            # اختبار حذف المستخدم
            delete_success = db.delete_user(test_user_id)
            
            if delete_success:
                print("   ✅ تم حذف المستخدم بنجاح")
                print("   ✅ User deleted successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة المستخدمين: {str(e)}")
        print(f"   ❌ User management test error: {str(e)}")
        return False

def test_user_api():
    """اختبار API إدارة المستخدمين"""
    print("\n🔌 اختبار API إدارة المستخدمين...")
    print("🔌 Testing User Management API...")
    
    try:
        # محاولة الوصول لـ API بدون تسجيل دخول
        response = requests.get('http://localhost:4040/api/users', timeout=5)
        
        if response.status_code in [401, 302]:
            print("   ✅ حماية API تعمل (مطلوب تسجيل دخول)")
            print("   ✅ API protection working (login required)")
        else:
            print(f"   ⚠️ استجابة API غير متوقعة: {response.status_code}")
            print(f"   ⚠️ Unexpected API response: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {str(e)}")
        print(f"   ❌ API test error: {str(e)}")
        return False

def test_user_pages():
    """اختبار صفحات إدارة المستخدمين"""
    print("\n📄 اختبار صفحات إدارة المستخدمين...")
    print("📄 Testing User Management Pages...")
    
    try:
        # اختبار صفحة إدارة المستخدمين
        response = requests.get('http://localhost:4040/users', timeout=5)
        
        if response.status_code == 302:  # Redirect to login
            print("   ✅ صفحة إدارة المستخدمين محمية")
            print("   ✅ User management page protected")
        elif response.status_code == 200:
            print("   ✅ صفحة إدارة المستخدمين تعمل")
            print("   ✅ User management page working")
        else:
            print(f"   ❌ خطأ في صفحة إدارة المستخدمين: {response.status_code}")
            return False
        
        # اختبار الصفحة العربية
        ar_response = requests.get('http://localhost:4040/users?language=ar', timeout=5)
        
        if ar_response.status_code in [200, 302]:
            print("   ✅ الدعم العربي لصفحة المستخدمين يعمل")
            print("   ✅ Arabic support for users page working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الصفحات: {str(e)}")
        print(f"   ❌ Pages test error: {str(e)}")
        return False

def show_user_info():
    """عرض معلومات المستخدمين"""
    print("\n" + "=" * 60)
    print("👥 معلومات المستخدمين")
    print("👥 User Information")
    print("=" * 60)
    
    try:
        db = DatabaseManager()
        users = db.get_all_users()
        stats = db.get_user_stats()
        
        print(f"📊 إحصائيات المستخدمين:")
        print(f"   المجموع: {stats['total']}")
        print(f"   المديرون: {stats['by_role'].get(3, 0)}")
        print(f"   المشرفون: {stats['by_role'].get(2, 0)}")
        print(f"   المشاهدون: {stats['by_role'].get(1, 0)}")
        print(f"   تسجيلات دخول حديثة: {stats['recent_logins']}")
        
        print(f"\n👤 المستخدمون الموجودون ({len(users)}):")
        for user in users:
            role_name = {1: 'مشاهد', 2: 'مشرف', 3: 'مدير'}.get(user['role'], 'غير محدد')
            status = 'نشط' if user['is_active'] else 'غير نشط'
            last_login = user['last_login'] or 'لم يسجل دخول'
            
            print(f"   • {user['username']} ({role_name}) - {status}")
            print(f"     البريد: {user['email'] or 'غير محدد'}")
            print(f"     آخر دخول: {last_login}")
        
        print(f"\n🌐 الوصول لإدارة المستخدمين:")
        print(f"   عربي: http://localhost:4040/users?language=ar")
        print(f"   English: http://localhost:4040/users?language=en")
        print(f"   🔑 تسجيل دخول: admin / admin123")
        
    except Exception as e:
        print(f"❌ خطأ في عرض معلومات المستخدمين: {str(e)}")

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام إدارة المستخدمين")
    print("🧪 User Management System Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار إدارة المستخدمين
    if test_user_management():
        tests_passed += 1
    
    # اختبار API
    if test_user_api():
        tests_passed += 1
    
    # اختبار الصفحات
    if test_user_pages():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار إدارة المستخدمين")
    print("📊 User Management Test Results")
    print("=" * 60)
    print(f"✅ نجح: {tests_passed}/{total_tests} اختبار")
    print(f"✅ Passed: {tests_passed}/{total_tests} tests")
    
    if tests_passed == total_tests:
        print("🎉 جميع اختبارات إدارة المستخدمين نجحت!")
        print("🎉 All user management tests passed!")
        print("✅ نظام إدارة المستخدمين يعمل بشكل مثالي")
        print("✅ User management system working perfectly")
    else:
        print("⚠️ بعض اختبارات إدارة المستخدمين فشلت")
        print("⚠️ Some user management tests failed")
    
    # عرض معلومات المستخدمين
    show_user_info()

if __name__ == "__main__":
    main()
