{% extends "base.html" %}

{% block title %}{{ g.get_text('user_management') }} - {{ g.get_text('camera_monitoring_system') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users"></i> {{ g.get_text('user_management') }}</h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus"></i> {{ g.get_text('add_user') }}
            </button>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ user_stats.total }}</h3>
                <p class="card-text">{{ g.get_text('total_users') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ user_stats.by_role.get(3, 0) }}</h3>
                <p class="card-text">{{ g.get_text('admin_role') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ user_stats.by_role.get(2, 0) }}</h3>
                <p class="card-text">{{ g.get_text('manager_role') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ user_stats.recent_logins }}</h3>
                <p class="card-text">{{ g.get_text('recent_logins') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> {{ g.get_text('user_list') }}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="usersTable">
                        <thead>
                            <tr>
                                <th>{{ g.get_text('id') }}</th>
                                <th>{{ g.get_text('username') }}</th>
                                <th>{{ g.get_text('email_address') }}</th>
                                <th>{{ g.get_text('role') }}</th>
                                <th>{{ g.get_text('account_status') }}</th>
                                <th>{{ g.get_text('last_login_time') }}</th>
                                <th>{{ g.get_text('actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr data-user-id="{{ user.id }}">
                                <td>{{ user.id }}</td>
                                <td>
                                    <strong>{{ user.username }}</strong>
                                    {% if user.id == current_user.id %}
                                    <span class="badge bg-info ms-1">{{ g.get_text('you') if g.get_text('you') != 'you' else 'أنت' }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.email or g.get_text('not_set') }}</td>
                                <td>
                                    {% if user.role == 3 %}
                                    <span class="badge bg-danger">{{ g.get_text('admin_role') }}</span>
                                    {% elif user.role == 2 %}
                                    <span class="badge bg-warning">{{ g.get_text('manager_role') }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ g.get_text('viewer_role') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{{ g.get_text('active') }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ g.get_text('inactive') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.last_login %}
                                    <small>{{ user.last_login }}</small>
                                    {% else %}
                                    <small class="text-muted">{{ g.get_text('never_logged_in') }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="editUser({{ user.id }})" title="{{ g.get_text('edit_user') }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="changePassword({{ user.id }})" title="{{ g.get_text('change_password') }}">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        {% if user.id != current_user.id and user.id != 1 %}
                                        {% if user.is_active %}
                                        <button class="btn btn-outline-secondary" onclick="toggleUserStatus({{ user.id }}, false)" title="{{ g.get_text('deactivate_user') }}">
                                            <i class="fas fa-user-slash"></i>
                                        </button>
                                        {% else %}
                                        <button class="btn btn-outline-success" onclick="toggleUserStatus({{ user.id }}, true)" title="{{ g.get_text('activate_user') }}">
                                            <i class="fas fa-user-check"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-outline-danger" onclick="deleteUser({{ user.id }})" title="{{ g.get_text('delete_user') }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> {{ g.get_text('add_user') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="newUsername" class="form-label">{{ g.get_text('username') }} *</label>
                        <input type="text" class="form-control" id="newUsername" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newEmail" class="form-label">{{ g.get_text('email_address') }}</label>
                        <input type="email" class="form-control" id="newEmail">
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">{{ g.get_text('password') }} *</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <small class="form-text text-muted">{{ g.get_text('password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newRole" class="form-label">{{ g.get_text('role') }} *</label>
                        <select class="form-select" id="newRole" required>
                            <option value="">{{ g.get_text('select_role') }}</option>
                            <option value="1">{{ g.get_text('viewer_role') }} - {{ g.get_text('viewer_description') }}</option>
                            <option value="2">{{ g.get_text('manager_role') }} - {{ g.get_text('manager_description') }}</option>
                            <option value="3">{{ g.get_text('admin_role') }} - {{ g.get_text('admin_description') }}</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ g.get_text('cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">{{ g.get_text('add_user') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-edit"></i> {{ g.get_text('edit_user') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">{{ g.get_text('username') }} *</label>
                        <input type="text" class="form-control" id="editUsername" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">{{ g.get_text('email_address') }}</label>
                        <input type="email" class="form-control" id="editEmail">
                    </div>
                    
                    <div class="mb-3">
                        <label for="editRole" class="form-label">{{ g.get_text('role') }} *</label>
                        <select class="form-select" id="editRole" required>
                            <option value="1">{{ g.get_text('viewer_role') }}</option>
                            <option value="2">{{ g.get_text('manager_role') }}</option>
                            <option value="3">{{ g.get_text('admin_role') }}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive">
                            <label class="form-check-label" for="editIsActive">
                                {{ g.get_text('active') }}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ g.get_text('cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">{{ g.get_text('save') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key"></i> {{ g.get_text('change_password') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <input type="hidden" id="passwordUserId">
                    
                    <div class="mb-3">
                        <label for="newUserPassword" class="form-label">{{ g.get_text('new_password') }} *</label>
                        <input type="password" class="form-control" id="newUserPassword" required>
                        <small class="form-text text-muted">{{ g.get_text('password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirmUserPassword" class="form-label">{{ g.get_text('confirm_password') }} *</label>
                        <input type="password" class="form-control" id="confirmUserPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ g.get_text('cancel') }}</button>
                <button type="button" class="btn btn-warning" onclick="updatePassword()">{{ g.get_text('change_password') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // Initialize DataTable if available
    if ($.fn.DataTable) {
        $('#usersTable').DataTable({
            "language": {
                "url": "{{ 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json' if g.lang == 'ar' else '' }}"
            },
            "order": [[ 0, "desc" ]],
            "pageLength": 25
        });
    }
});

function addUser() {
    const formData = {
        username: $('#newUsername').val(),
        email: $('#newEmail').val(),
        password: $('#newPassword').val(),
        role: parseInt($('#newRole').val())
    };
    
    // Validation
    if (!formData.username || !formData.password || !formData.role) {
        alert('{{ g.get_text("missing_required_field") }}');
        return;
    }
    
    if (formData.password.length < 6) {
        alert('{{ g.get_text("weak_password") }}');
        return;
    }
    
    $.ajax({
        url: '/api/users',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#addUserModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('خطأ: ' + xhr.responseJSON.error);
        }
    });
}

function editUser(userId) {
    // Get user data from table row
    const row = $(`tr[data-user-id="${userId}"]`);
    const username = row.find('td:nth-child(2) strong').text();
    const email = row.find('td:nth-child(3)').text();
    const roleText = row.find('td:nth-child(4) .badge').text();
    const isActive = row.find('td:nth-child(5) .badge').hasClass('bg-success');
    
    // Map role text to value
    let roleValue = 1;
    if (roleText.includes('{{ g.get_text("admin_role") }}')) roleValue = 3;
    else if (roleText.includes('{{ g.get_text("manager_role") }}')) roleValue = 2;
    
    // Fill form
    $('#editUserId').val(userId);
    $('#editUsername').val(username);
    $('#editEmail').val(email === '{{ g.get_text("not_set") }}' ? '' : email);
    $('#editRole').val(roleValue);
    $('#editIsActive').prop('checked', isActive);
    
    $('#editUserModal').modal('show');
}

function updateUser() {
    const userId = $('#editUserId').val();
    const formData = {
        username: $('#editUsername').val(),
        email: $('#editEmail').val(),
        role: parseInt($('#editRole').val()),
        is_active: $('#editIsActive').is(':checked')
    };
    
    $.ajax({
        url: `/api/users/${userId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            $('#editUserModal').modal('hide');
            location.reload();
        },
        error: function(xhr) {
            alert('خطأ: ' + xhr.responseJSON.error);
        }
    });
}

function changePassword(userId) {
    $('#passwordUserId').val(userId);
    $('#newUserPassword').val('');
    $('#confirmUserPassword').val('');
    $('#changePasswordModal').modal('show');
}

function updatePassword() {
    const userId = $('#passwordUserId').val();
    const newPassword = $('#newUserPassword').val();
    const confirmPassword = $('#confirmUserPassword').val();
    
    if (newPassword !== confirmPassword) {
        alert('{{ g.get_text("password_mismatch") }}');
        return;
    }
    
    if (newPassword.length < 6) {
        alert('{{ g.get_text("weak_password") }}');
        return;
    }
    
    $.ajax({
        url: `/api/users/${userId}/password`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ new_password: newPassword }),
        success: function(response) {
            $('#changePasswordModal').modal('hide');
            alert('{{ g.get_text("password_changed") }}');
        },
        error: function(xhr) {
            alert('خطأ: ' + xhr.responseJSON.error);
        }
    });
}

function toggleUserStatus(userId, activate) {
    $.ajax({
        url: `/api/users/${userId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ is_active: activate }),
        success: function(response) {
            location.reload();
        },
        error: function(xhr) {
            alert('خطأ: ' + xhr.responseJSON.error);
        }
    });
}

function deleteUser(userId) {
    if (confirm('{{ g.get_text("confirm_delete_user") }}')) {
        $.ajax({
            url: `/api/users/${userId}`,
            method: 'DELETE',
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('خطأ: ' + xhr.responseJSON.error);
            }
        });
    }
}
</script>
{% endblock %}
