import os
from datetime import timedelta

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'

    # Database configuration
    DATABASE_PATH = 'camera_system.db'

    # Server configuration - محسن للأداء
    HOST = '0.0.0.0'
    PORT = 4040
    DEBUG = False  # تم تغييره لتحسين الأداء
    THREADED = True  # تفعيل threading

    # Camera configuration - محسن
    CAMERA_TIMEOUT = 15  # تقليل timeout
    STREAM_QUALITY = 'medium'  # جودة متوسطة للأداء
    MAX_CAMERAS = 20  # تقليل العدد الأقصى
    FRAME_RATE = 15  # تقليل معدل الإطارات
    BUFFER_SIZE = 1  # تقليل buffer

    # Recording configuration
    RECORDING_PATH = 'recordings'
    MAX_RECORDING_DAYS = 7  # تقليل فترة الحفظ

    # Motion detection - محسن
    MOTION_THRESHOLD = 30  # زيادة threshold
    MOTION_MIN_AREA = 1000  # زيادة المساحة الدنيا
    MOTION_DETECTION_INTERVAL = 2  # فحص كل ثانيتين

    # Performance settings - جديد
    DATABASE_POOL_SIZE = 10
    MAX_CONCURRENT_STREAMS = 5
    CACHE_TIMEOUT = 300  # 5 دقائق
    ENABLE_COMPRESSION = True

    # Map configuration
    DEFAULT_MAP_CENTER = [40.7128, -74.0060]  # New York coordinates
    DEFAULT_ZOOM = 10

    # User session
    PERMANENT_SESSION_LIFETIME = timedelta(hours=12)  # تقليل مدة الجلسة

    # Logging - محسن
    LOG_LEVEL = 'WARNING'  # تقليل مستوى السجلات
    MAX_LOG_SIZE = 1024 * 1024  # 1MB
    
    # Email configuration (for notifications)
    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # WhatsApp configuration (optional)
    WHATSAPP_API_KEY = os.environ.get('WHATSAPP_API_KEY')
    
    # Camera types supported
    SUPPORTED_CAMERA_TYPES = [
        'dahua',
        'hikvision', 
        'ip_camera',
        'usb_camera',
        'rtsp_stream',
        'onvif'
    ]
    
    # Default RTSP ports
    DEFAULT_RTSP_PORTS = {
        'dahua': 554,
        'hikvision': 554,
        'generic': 554
    }
    
    # User roles
    USER_ROLES = {
        'viewer': 1,
        'manager': 2,
        'admin': 3
    }
