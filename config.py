import os
from datetime import timedelta

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # Database configuration
    DATABASE_PATH = 'camera_system.db'
    
    # Server configuration
    HOST = '0.0.0.0'
    PORT = 4040
    DEBUG = True
    
    # Camera configuration
    CAMERA_TIMEOUT = 30  # seconds
    STREAM_QUALITY = 'medium'  # low, medium, high
    MAX_CAMERAS = 50
    
    # Recording configuration
    RECORDING_PATH = 'recordings'
    MAX_RECORDING_DAYS = 30
    
    # Motion detection
    MOTION_THRESHOLD = 25
    MOTION_MIN_AREA = 500
    
    # Map configuration
    DEFAULT_MAP_CENTER = [40.7128, -74.0060]  # New York coordinates
    DEFAULT_ZOOM = 10
    
    # User session
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Email configuration (for notifications)
    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # WhatsApp configuration (optional)
    WHATSAPP_API_KEY = os.environ.get('WHATSAPP_API_KEY')
    
    # Camera types supported
    SUPPORTED_CAMERA_TYPES = [
        'dahua',
        'hikvision', 
        'ip_camera',
        'usb_camera',
        'rtsp_stream',
        'onvif'
    ]
    
    # Default RTSP ports
    DEFAULT_RTSP_PORTS = {
        'dahua': 554,
        'hikvision': 554,
        'generic': 554
    }
    
    # User roles
    USER_ROLES = {
        'viewer': 1,
        'manager': 2,
        'admin': 3
    }
